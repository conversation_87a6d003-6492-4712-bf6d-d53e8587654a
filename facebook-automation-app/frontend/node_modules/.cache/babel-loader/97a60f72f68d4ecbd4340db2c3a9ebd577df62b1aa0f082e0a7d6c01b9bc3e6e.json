{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon, Facebook as FacebookIcon, Login as LoginIcon, CheckCircle as CheckCircleIcon, Visibility as VisibilityIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest,\n  onCheck,\n  onFacebookLogin,\n  onFacebookLoginComplete,\n  onFacebookLoginTerminate,\n  loginStatusPolling\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isLoginInProgress = loginStatusPolling && loginStatusPolling.has(profile.id);\n  // Show Complete Login button ONLY when login is actually in progress\n  const shouldShowCompleteLogin = isLoginInProgress;\n  const isReady = profile.status === 'active' && isLoggedIn;\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: '0.75rem'\n            },\n            children: \"Facebook:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), isLoggedIn ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#4caf50'\n              },\n              children: \"Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this) : shouldShowCompleteLogin ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(RefreshIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#2196f3'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#2196f3'\n              },\n              children: \"Login in Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#ff9800'\n              },\n              children: \"Not Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 24\n          }, this),\n          onClick: () => onCheck(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          disabled: !isLoggedIn,\n          children: \"Check\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), shouldShowCompleteLogin && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"contained\",\n          color: \"success\",\n          startIcon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 26\n          }, this),\n          onClick: () => onFacebookLoginComplete(profile),\n          sx: {\n            textTransform: 'none',\n            backgroundColor: '#4caf50',\n            '&:hover': {\n              backgroundColor: '#45a049'\n            }\n          },\n          children: \"Complete Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onCheck(profile);\n            handleMenuClose();\n          },\n          disabled: !isLoggedIn,\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Check Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), shouldShowCompleteLogin ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginComplete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Complete Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this) : isLoggedIn ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginComplete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Update Login Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLogin(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(FacebookIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#1877f2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Login to Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginTerminate(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Terminate Browser Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error\n  } = useQuery('profiles', () => profilesAPI.getAll().then(response => response.data), {\n    onSuccess: data => {\n      // Handle both array and object with profiles property\n      const profilesList = (data === null || data === void 0 ? void 0 : data.profiles) || data || [];\n      setProfiles(profilesList);\n    },\n    onError: error => {\n      toast.error('Failed to load profiles');\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(profileId => profilesAPI.testProxy(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n      } else if (result.status === 'no_proxy') {\n        toast.info('No proxy configured for this profile');\n      } else {\n        toast.error(`Proxy test failed: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response3, _error$response4, _error$response4$data;\n      console.error('Proxy test error:', error);\n\n      // Handle specific error cases\n      if ((error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n        toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n      } else if ((error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) === \"Endpoint not found\") {\n        toast.warning('Test feature not available in current backend version.');\n      } else {\n        var _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || (error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || (error === null || error === void 0 ? void 0 : error.message) || 'Proxy test failed';\n        toast.error(errorMessage);\n      }\n    }\n  });\n\n  // Check profile mutation\n  const checkProfileMutation = useMutation(profileId => profilesAPI.checkProfile(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'browser_launched') {\n        toast.success('Antidetect browser opened with saved Facebook cookies!');\n      } else if (result.status === 'no_cookies') {\n        toast.warning('No Facebook cookies found. Please login to Facebook first.');\n      } else if (result.status === 'cookies_expired') {\n        toast.warning('Facebook cookies have expired. Please login again.');\n      } else {\n        toast.error(`Failed to check profile: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response7, _error$response7$data;\n      console.error('Check profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to check profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(profileId => profilesAPI.facebookLogin(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n        toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n        // Show instructions dialog\n        setLoginInstructions(result.instructions || []);\n        setLoginInstructionsOpen(true);\n\n        // Start polling for login status\n        startLoginStatusPolling(result.profile_id);\n      } else if (result.status === 'session_exists') {\n        toast.info('Browser session already active. Complete login in the existing browser window.');\n        setLoginInstructions([\"Browser session is already running\", \"Complete Facebook login in the existing browser window\", \"Click 'Complete Login' button when done\"]);\n        setLoginInstructionsOpen(true);\n      } else {\n        toast.error(`Failed to start login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response8, _error$response8$data;\n      console.error('Facebook login error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to start Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginCompleteMutation = useMutation(({\n    profileId,\n    facebookData\n  }) => profilesAPI.facebookLoginComplete(profileId, facebookData), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_complete') {\n        toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n        queryClient.invalidateQueries('profiles');\n      } else {\n        toast.error(`Failed to complete login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response9, _error$response9$data;\n      console.error('Facebook login complete error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to complete Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginTerminateMutation = useMutation(profileId => profilesAPI.facebookLoginTerminate(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      toast.success('Browser session terminated successfully.');\n      stopLoginStatusPolling(result.profile_id);\n      setLoginInstructionsOpen(false);\n    },\n    onError: error => {\n      var _error$response0, _error$response0$data;\n      console.error('Facebook login terminate error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to terminate browser session';\n      toast.error(errorMessage);\n    }\n  });\n  const profiles = Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.profiles) ? profilesData.profiles : Array.isArray(profilesData) ? profilesData : [];\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy' ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}` : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [`Profile: ${profile.name}`, `Status: ${profile.status}`, `Proxy: ${proxyInfo}`, `Language: ${profile.language || 'en-US'}`, `Timezone: ${profile.timezone || 'UTC'}`, `Created: ${new Date(profile.created_at).toLocaleDateString()}`].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, {\n      duration: 5000\n    });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n  const handleCheck = profile => {\n    checkProfileMutation.mutate(profile.id);\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleFacebookLogin = profile => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n  const handleFacebookLoginTerminate = profile => {\n    if (window.confirm(`Are you sure you want to terminate the browser session for \"${profile.name}\"?`)) {\n      facebookLoginTerminateMutation.mutate(profile.id);\n    }\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = profileId => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n  const stopLoginStatusPolling = profileId => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach(intervalId => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n  const handleFacebookLoginComplete = async profile => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n    try {\n      // Try to get Facebook status from the browser session first\n      const statusResponse = await profilesAPI.getFacebookStatus(profile.id);\n      const facebookData = statusResponse.data.facebook_data || {};\n\n      // Complete login with extracted data or default data\n      facebookLoginCompleteMutation.mutate({\n        profileId: profile.id,\n        facebookData: {\n          email: facebookData.email || \"<EMAIL>\",\n          // This would be extracted from browser\n          username: facebookData.username || \"facebook_user\",\n          user_id: facebookData.user_id || \"123456789\"\n        }\n      });\n    } catch (error) {\n      console.log('Could not extract Facebook data from browser, using default data');\n      // Fallback to default data if extraction fails\n      facebookLoginCompleteMutation.mutate({\n        profileId: profile.id,\n        facebookData: {\n          email: \"<EMAIL>\",\n          // This would be extracted from browser\n          username: \"facebook_user\",\n          user_id: \"123456789\"\n        }\n      });\n    }\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 784,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 24\n          }, this),\n          onClick: () => queryClient.invalidateQueries('profiles'),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 21\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest,\n          onCheck: handleCheck,\n          onFacebookLogin: handleFacebookLogin,\n          onFacebookLoginComplete: handleFacebookLoginComplete,\n          onFacebookLoginTerminate: handleFacebookLoginTerminate,\n          loginStatusPolling: loginStatusPolling\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 873,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 982,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 983,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 998,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 999,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 997,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1006,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1007,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1005,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1004,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1015,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        testProxyMutation.mutate(selectedProfile.id);\n                      } else {\n                        // Test proxy configuration without saving\n                        const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                        const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                        toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, {\n                          duration: 4000\n                        });\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: loginInstructionsOpen,\n      onClose: () => setLoginInstructionsOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n          sx: {\n            color: '#1877f2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 11\n        }, this), \"Facebook Login Instructions\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"An antidetect browser window has opened with your profile configuration. Follow these steps to complete Facebook login:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ol\",\n          sx: {\n            pl: 2\n          },\n          children: loginInstructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(Box, {\n            component: \"li\",\n            sx: {\n              mb: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: instruction\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1173,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1172,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Important:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1180,\n              columnNumber: 15\n            }, this), \" After successfully logging in to Facebook, click the green \\\"Complete Login\\\" button on the profile card or in the profile menu to update the profile status to \\\"Ready\\\".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mt: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tip:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 15\n            }, this), \" The \\\"Complete Login\\\" button will appear automatically when the browser session is active or when the profile status is ready for completion.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setLoginInstructionsOpen(false),\n          color: \"primary\",\n          children: \"Got it\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            // Find the profile that has active login session\n            const activeProfile = profiles.find(p => loginStatusPolling.has(p.id));\n            if (activeProfile) {\n              handleFacebookLoginTerminate(activeProfile);\n            }\n          },\n          color: \"warning\",\n          variant: \"outlined\",\n          children: \"Terminate Browser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 793,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"8PpmhDGAcIf6JbB40xVBocynln0=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "Facebook", "FacebookIcon", "<PERSON><PERSON>", "LoginIcon", "CheckCircle", "CheckCircleIcon", "Visibility", "VisibilityIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "onCheck", "onFacebookLogin", "onFacebookLoginComplete", "onFacebookLoginTerminate", "loginStatusPolling", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "isLoggedIn", "facebook_email", "facebook_user_id", "isLoginInProgress", "has", "id", "shouldShowCompleteLogin", "isReady", "status", "getStatusColor", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "flexWrap", "startIcon", "textTransform", "disabled", "backgroundColor", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "loginInstructionsOpen", "setLoginInstructionsOpen", "loginInstructions", "setLoginInstructions", "setLoginStatusPolling", "Map", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "getAll", "then", "response", "onSuccess", "profilesList", "profiles", "onError", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "console", "errorMessage", "detail", "message", "updateMutation", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testProxyMutation", "profileId", "testProxy", "result", "response_time", "info", "_error$response3", "_error$response4", "_error$response4$data", "warning", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "checkProfileMutation", "checkProfile", "_error$response7", "_error$response7$data", "facebookLoginMutation", "facebookLogin", "instructions", "startLoginStatusPolling", "profile_id", "_error$response8", "_error$response8$data", "facebookLoginCompleteMutation", "facebookData", "facebookLoginComplete", "profile_name", "_error$response9", "_error$response9$data", "facebookLoginTerminateMutation", "facebookLoginTerminate", "stopLoginStatusPolling", "_error$response0", "_error$response0$data", "Array", "isArray", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "proxyInfo", "profileInfo", "Date", "created_at", "toLocaleDateString", "join", "duration", "setTimeout", "handleCheck", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "handleFacebookLogin", "handleFacebookLoginTerminate", "intervalId", "setInterval", "facebookLoginStatus", "login_status", "set", "get", "clearInterval", "newMap", "useEffect", "for<PERSON>ach", "handleFacebookLoginComplete", "statusResponse", "getFacebookStatus", "facebook_data", "email", "user_id", "log", "className", "severity", "length", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "authInfo", "component", "pl", "instruction", "index", "activeProfile", "find", "p", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n  Refresh as RefreshIcon,\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n  Facebook as FacebookIcon,\n  Login as LoginIcon,\n  CheckCircle as CheckCircleIcon,\n  Visibility as VisibilityIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest, onCheck, onFacebookLogin, onFacebookLoginComplete, onFacebookLoginTerminate, loginStatusPolling }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isLoginInProgress = loginStatusPolling && loginStatusPolling.has(profile.id);\n  // Show Complete Login button ONLY when login is actually in progress\n  const shouldShowCompleteLogin = isLoginInProgress;\n  const isReady = profile.status === 'active' && isLoggedIn;\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Box item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Box>\n\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <FacebookIcon fontSize=\"small\" color=\"action\" />\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.75rem' }}>\n              Facebook:\n            </Typography>\n            {isLoggedIn ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#4caf50' }}>\n                  Logged In\n                </Typography>\n              </Box>\n            ) : shouldShowCompleteLogin ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <RefreshIcon fontSize=\"small\" sx={{ color: '#2196f3' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#2196f3' }}>\n                  Login in Progress\n                </Typography>\n              </Box>\n            ) : (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <LoginIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#ff9800' }}>\n                  Not Logged In\n                </Typography>\n              </Box>\n            )}\n          </Box>\n        </Box>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<VisibilityIcon />}\n            onClick={() => onCheck(profile)}\n            sx={{ textTransform: 'none' }}\n            disabled={!isLoggedIn}\n          >\n            Check\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n          {shouldShowCompleteLogin && (\n            <Button\n              size=\"small\"\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<CheckCircleIcon />}\n              onClick={() => onFacebookLoginComplete(profile)}\n              sx={{\n                textTransform: 'none',\n                backgroundColor: '#4caf50',\n                '&:hover': {\n                  backgroundColor: '#45a049'\n                }\n              }}\n            >\n              Complete Login\n            </Button>\n          )}\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onCheck(profile); handleMenuClose(); }} disabled={!isLoggedIn}>\n            <ListItemIcon>\n              <VisibilityIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Check Profile</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          {shouldShowCompleteLogin ? (\n            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n              </ListItemIcon>\n              <ListItemText>Complete Login</ListItemText>\n            </MenuItem>\n          ) : isLoggedIn ? (\n            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n              </ListItemIcon>\n              <ListItemText>Update Login Info</ListItemText>\n            </MenuItem>\n          ) : (\n            <MenuItem onClick={() => { onFacebookLogin(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <FacebookIcon fontSize=\"small\" sx={{ color: '#1877f2' }} />\n              </ListItemIcon>\n              <ListItemText>Login to Facebook</ListItemText>\n            </MenuItem>\n          )}\n\n          <MenuItem onClick={() => { onFacebookLoginTerminate(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <RefreshIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n            </ListItemIcon>\n            <ListItemText>Terminate Browser Session</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error } = useQuery(\n    'profiles',\n    () => profilesAPI.getAll().then(response => response.data),\n    {\n      onSuccess: (data) => {\n        // Handle both array and object with profiles property\n        const profilesList = data?.profiles || data || [];\n        setProfiles(profilesList);\n      },\n      onError: (error) => {\n        toast.error('Failed to load profiles');\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(\n    (profileId) => profilesAPI.testProxy(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n        } else if (result.status === 'no_proxy') {\n          toast.info('No proxy configured for this profile');\n        } else {\n          toast.error(`Proxy test failed: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Proxy test error:', error);\n\n        // Handle specific error cases\n        if (error?.response?.status === 404) {\n          toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n        } else if (error?.response?.data?.message === \"Endpoint not found\") {\n          toast.warning('Test feature not available in current backend version.');\n        } else {\n          const errorMessage = error?.response?.data?.detail ||\n                              error?.response?.data?.message ||\n                              error?.message ||\n                              'Proxy test failed';\n          toast.error(errorMessage);\n        }\n      },\n    }\n  );\n\n  // Check profile mutation\n  const checkProfileMutation = useMutation(\n    (profileId) => profilesAPI.checkProfile(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'browser_launched') {\n          toast.success('Antidetect browser opened with saved Facebook cookies!');\n        } else if (result.status === 'no_cookies') {\n          toast.warning('No Facebook cookies found. Please login to Facebook first.');\n        } else if (result.status === 'cookies_expired') {\n          toast.warning('Facebook cookies have expired. Please login again.');\n        } else {\n          toast.error(`Failed to check profile: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Check profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to check profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(\n    (profileId) => profilesAPI.facebookLogin(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n          toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n          // Show instructions dialog\n          setLoginInstructions(result.instructions || []);\n          setLoginInstructionsOpen(true);\n\n          // Start polling for login status\n          startLoginStatusPolling(result.profile_id);\n        } else if (result.status === 'session_exists') {\n          toast.info('Browser session already active. Complete login in the existing browser window.');\n          setLoginInstructions([\n            \"Browser session is already running\",\n            \"Complete Facebook login in the existing browser window\",\n            \"Click 'Complete Login' button when done\"\n          ]);\n          setLoginInstructionsOpen(true);\n        } else {\n          toast.error(`Failed to start login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to start Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginCompleteMutation = useMutation(\n    ({ profileId, facebookData }) => profilesAPI.facebookLoginComplete(profileId, facebookData),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_complete') {\n          toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n          queryClient.invalidateQueries('profiles');\n        } else {\n          toast.error(`Failed to complete login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login complete error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to complete Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginTerminateMutation = useMutation(\n    (profileId) => profilesAPI.facebookLoginTerminate(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        toast.success('Browser session terminated successfully.');\n        stopLoginStatusPolling(result.profile_id);\n        setLoginInstructionsOpen(false);\n      },\n      onError: (error) => {\n        console.error('Facebook login terminate error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to terminate browser session';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const profiles = Array.isArray(profilesData?.profiles)\n    ? profilesData.profiles\n    : Array.isArray(profilesData)\n    ? profilesData\n    : [];\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy'\n      ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`\n      : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [\n      `Profile: ${profile.name}`,\n      `Status: ${profile.status}`,\n      `Proxy: ${proxyInfo}`,\n      `Language: ${profile.language || 'en-US'}`,\n      `Timezone: ${profile.timezone || 'UTC'}`,\n      `Created: ${new Date(profile.created_at).toLocaleDateString()}`\n    ].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, { duration: 5000 });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n\n  const handleCheck = (profile) => {\n    checkProfileMutation.mutate(profile.id);\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleFacebookLogin = (profile) => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n\n  const handleFacebookLoginTerminate = (profile) => {\n    if (window.confirm(`Are you sure you want to terminate the browser session for \"${profile.name}\"?`)) {\n      facebookLoginTerminateMutation.mutate(profile.id);\n    }\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = (profileId) => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n\n  const stopLoginStatusPolling = (profileId) => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach((intervalId) => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n\n  const handleFacebookLoginComplete = async (profile) => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n\n    try {\n      // Try to get Facebook status from the browser session first\n      const statusResponse = await profilesAPI.getFacebookStatus(profile.id);\n      const facebookData = statusResponse.data.facebook_data || {};\n\n      // Complete login with extracted data or default data\n      facebookLoginCompleteMutation.mutate({\n        profileId: profile.id,\n        facebookData: {\n          email: facebookData.email || \"<EMAIL>\", // This would be extracted from browser\n          username: facebookData.username || \"facebook_user\",\n          user_id: facebookData.user_id || \"123456789\"\n        }\n      });\n    } catch (error) {\n      console.log('Could not extract Facebook data from browser, using default data');\n      // Fallback to default data if extraction fails\n      facebookLoginCompleteMutation.mutate({\n        profileId: profile.id,\n        facebookData: {\n          email: \"<EMAIL>\", // This would be extracted from browser\n          username: \"facebook_user\",\n          user_id: \"123456789\"\n        }\n      });\n    }\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => queryClient.invalidateQueries('profiles')}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n                onCheck={handleCheck}\n                onFacebookLogin={handleFacebookLogin}\n                onFacebookLoginComplete={handleFacebookLoginComplete}\n                onFacebookLoginTerminate={handleFacebookLoginTerminate}\n                loginStatusPolling={loginStatusPolling}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            testProxyMutation.mutate(selectedProfile.id);\n                          } else {\n                            // Test proxy configuration without saving\n                            const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                            const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                            toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, { duration: 4000 });\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        {testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'}\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Facebook Login Instructions Dialog */}\n      <Dialog\n        open={loginInstructionsOpen}\n        onClose={() => setLoginInstructionsOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <FacebookIcon sx={{ color: '#1877f2' }} />\n          Facebook Login Instructions\n        </DialogTitle>\n        <DialogContent>\n          <Alert severity=\"info\" sx={{ mb: 2 }}>\n            An antidetect browser window has opened with your profile configuration. Follow these steps to complete Facebook login:\n          </Alert>\n\n          <Box component=\"ol\" sx={{ pl: 2 }}>\n            {loginInstructions.map((instruction, index) => (\n              <Box component=\"li\" key={index} sx={{ mb: 1 }}>\n                <Typography variant=\"body2\">{instruction}</Typography>\n              </Box>\n            ))}\n          </Box>\n\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              <strong>Important:</strong> After successfully logging in to Facebook,\n              click the green \"Complete Login\" button on the profile card or in the profile menu\n              to update the profile status to \"Ready\".\n            </Typography>\n          </Alert>\n\n          <Alert severity=\"success\" sx={{ mt: 1 }}>\n            <Typography variant=\"body2\">\n              <strong>Tip:</strong> The \"Complete Login\" button will appear automatically\n              when the browser session is active or when the profile status is ready for completion.\n            </Typography>\n          </Alert>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setLoginInstructionsOpen(false)}\n            color=\"primary\"\n          >\n            Got it\n          </Button>\n          <Button\n            onClick={() => {\n              // Find the profile that has active login session\n              const activeProfile = profiles.find(p => loginStatusPolling.has(p.id));\n              if (activeProfile) {\n                handleFacebookLoginTerminate(activeProfile);\n              }\n            }}\n            color=\"warning\"\n            variant=\"outlined\"\n          >\n            Terminate Browser\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EACrBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,OAAO;EAAEC,eAAe;EAAEC,uBAAuB;EAAEC,wBAAwB;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EAC3J,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAM+E,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,UAAU,GAAGhB,OAAO,CAACiB,cAAc,IAAIjB,OAAO,CAACkB,gBAAgB;EACrE,MAAMC,iBAAiB,GAAGX,kBAAkB,IAAIA,kBAAkB,CAACY,GAAG,CAACpB,OAAO,CAACqB,EAAE,CAAC;EAClF;EACA,MAAMC,uBAAuB,GAAGH,iBAAiB;EACjD,MAAMI,OAAO,GAAGvB,OAAO,CAACwB,MAAM,KAAK,QAAQ,IAAIR,UAAU;EAEzD,MAAMS,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACE5B,OAAA,CAAC5D,IAAI;IAAC0F,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BhC,OAAA,CAAC3D,WAAW;MAAA2F,QAAA,gBACVhC,OAAA,CAAC9D,GAAG;QAAC4F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7FhC,OAAA,CAAC9D,GAAG;UAAC4F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDhC,OAAA,CAAC5C,MAAM;YACL0E,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAED5B,OAAO,CAACoC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACT9C,OAAA,CAAC9D,GAAG;YAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvD5B,OAAO,CAACoC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACb9C,OAAA,CAACxD,IAAI;cACHyG,KAAK,EAAE7C,OAAO,CAACwB,MAAM,IAAI,UAAW;cACpCsB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,cAAc,CAACzB,OAAO,CAACwB,MAAM;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9C,OAAA,CAACvD,UAAU;UAAC2G,OAAO,EAAEpC,eAAgB;UAAAgB,QAAA,eACnChC,OAAA,CAAChC,YAAY;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN9C,OAAA,CAAC7C,OAAO;QAAC2E,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1B9C,OAAA,CAACzD,IAAI;QAAC+G,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzBhC,OAAA,CAACzD,IAAI;UAACiH,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfhC,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDhC,OAAA,CAACtB,YAAY;cAACoD,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9C,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACP9C,OAAA,CAACzD,IAAI;UAACiH,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfhC,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDhC,OAAA,CAAChB,YAAY;cAAC8C,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9C,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjD5B,OAAO,CAACuD,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACP9C,OAAA,CAACzD,IAAI;UAACiH,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfhC,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDhC,OAAA,CAACpB,YAAY;cAACkD,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9C,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjD5B,OAAO,CAACwD,UAAU,IAAIxD,OAAO,CAACwD,UAAU,KAAK,UAAU,GACpDxD,OAAO,CAACwD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACP9C,OAAA,CAAC9D,GAAG;UAACsH,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACdhC,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDhC,OAAA,CAAClB,YAAY;cAACgD,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtE9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9C,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjD5B,OAAO,CAACyD,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eAEP9C,OAAA,CAAC9D,GAAG;QAAC4F,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,cAAc,EAAE;QAAgB,CAAE;QAAAF,QAAA,eACzFhC,OAAA,CAAC9D,GAAG;UAAC4F,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAA/B,QAAA,gBACzDhC,OAAA,CAACd,YAAY;YAACwE,QAAQ,EAAC,OAAO;YAACP,KAAK,EAAC;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD9C,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAACrB,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAU,CAAE;YAAA1B,QAAA,EAAC;UAEhF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ1B,UAAU,gBACTpB,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DhC,OAAA,CAACV,eAAe;cAACoE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9D9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,GACJpB,uBAAuB,gBACzB1B,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DhC,OAAA,CAACxB,WAAW;cAACkF,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEN9C,OAAA,CAAC9D,GAAG;YAAC4F,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DhC,OAAA,CAACZ,SAAS;cAACsE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD9C,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9C,OAAA,CAAC9D,GAAG;QAAC4F,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAhC,QAAA,gBAC5DhC,OAAA,CAAC1D,MAAM;UACL4G,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBkB,SAAS,eAAEjE,OAAA,CAAC1B,QAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAM7C,MAAM,CAACH,OAAO,CAAE;UAC/B0B,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAAC1D,MAAM;UACL4G,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBkB,SAAS,eAAEjE,OAAA,CAACR,cAAc;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BM,OAAO,EAAEA,CAAA,KAAM5C,OAAO,CAACJ,OAAO,CAAE;UAChC0B,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAC9BC,QAAQ,EAAE,CAAC/C,UAAW;UAAAY,QAAA,EACvB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAAC1D,MAAM;UACL4G,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdkB,SAAS,eAAEjE,OAAA,CAAC9B,QAAQ;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAM/C,MAAM,CAACD,OAAO,CAAE;UAC/B0B,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRpB,uBAAuB,iBACtB1B,OAAA,CAAC1D,MAAM;UACL4G,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,WAAW;UACnBI,KAAK,EAAC,SAAS;UACfc,SAAS,eAAEjE,OAAA,CAACV,eAAe;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/BM,OAAO,EAAEA,CAAA,KAAM1C,uBAAuB,CAACN,OAAO,CAAE;UAChD0B,EAAE,EAAE;YACFoC,aAAa,EAAE,MAAM;YACrBE,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB;UACF,CAAE;UAAApC,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9C,OAAA,CAAC3C,IAAI;QACHyD,QAAQ,EAAEA,QAAS;QACnBuD,IAAI,EAAEC,OAAO,CAACxD,QAAQ,CAAE;QACxByD,OAAO,EAAEpD,eAAgB;QAAAa,QAAA,gBAEzBhC,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE/C,MAAM,CAACD,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBAC/DhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAAC9B,QAAQ;cAACwF,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE7C,MAAM,CAACH,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBAC/DhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAAC1B,QAAQ;cAACoF,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE5C,OAAO,CAACJ,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAACgD,QAAQ,EAAE,CAAC/C,UAAW;UAAAY,QAAA,gBACvFhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAACR,cAAc;cAACkE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEX9C,OAAA,CAAC7C,OAAO;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEVpB,uBAAuB,gBACtB1B,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE1C,uBAAuB,CAACN,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBAChFhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAACV,eAAe;cAACoE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,GACT1B,UAAU,gBACZpB,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE1C,uBAAuB,CAACN,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBAChFhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAACV,eAAe;cAACoE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,gBAEX9C,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE3C,eAAe,CAACL,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBACxEhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAACd,YAAY;cAACwE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACX,eAED9C,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAEzC,wBAAwB,CAACP,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBACjFhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAACxB,WAAW;cAACkF,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAyB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAEX9C,OAAA,CAAC7C,OAAO;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEX9C,OAAA,CAAC9C,QAAQ;UAACkG,OAAO,EAAEA,CAAA,KAAM;YAAE9C,QAAQ,CAACF,OAAO,CAAC;YAAEe,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAa,QAAA,gBACjEhC,OAAA,CAAC1C,YAAY;YAAA0E,QAAA,eACXhC,OAAA,CAAC5B,UAAU;cAACsF,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACf9C,OAAA,CAACzC,YAAY;YAAAyE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAACjC,EAAA,CArQQV,WAAW;AAAAqE,EAAA,GAAXrE,WAAW;AAuQpB,SAASsE,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4I,cAAc,EAAEC,iBAAiB,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8I,eAAe,EAAEC,kBAAkB,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgJ,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjJ,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2E,kBAAkB,EAAEyE,qBAAqB,CAAC,GAAGpJ,QAAQ,CAAC,IAAIqJ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvJ,QAAQ,CAAC;IACvCuG,IAAI,EAAE,EAAE;IACRiD,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9BhC,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjB+B,aAAa,EAAE,KAAK;IACpBhC,UAAU,EAAE,UAAU;IACtBiC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGtG,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEuG;EAAY,CAAC,GAAGpG,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAEqG,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAG7G,QAAQ,CACvD,UAAU,EACV,MAAMI,WAAW,CAAC0G,MAAM,CAAC,CAAC,CAACC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACN,IAAI,CAAC,EAC1D;IACEO,SAAS,EAAGP,IAAI,IAAK;MACnB;MACA,MAAMQ,YAAY,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAIT,IAAI,IAAI,EAAE;MACjDD,WAAW,CAACS,YAAY,CAAC;IAC3B,CAAC;IACDE,OAAO,EAAGP,KAAK,IAAK;MAClB1G,KAAK,CAAC0G,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAGpH,WAAW,CAACG,WAAW,CAACkH,MAAM,EAAE;IACrDL,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCpC,mBAAmB,CAAC,KAAK,CAAC;MAC1BqC,SAAS,CAAC,CAAC;MACXrH,KAAK,CAACsH,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MAClBC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAa,eAAA,GAALb,KAAK,CAAEG,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBhB,IAAI,cAAAiB,oBAAA,uBAArBA,oBAAA,CAAuBG,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9C5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAMG,cAAc,GAAG/H,WAAW,CAChC,CAAC;IAAE+B,EAAE;IAAE0E;EAAK,CAAC,KAAKtG,WAAW,CAAC6H,MAAM,CAACjG,EAAE,EAAE0E,IAAI,CAAC,EAC9C;IACEO,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzClC,iBAAiB,CAAC,KAAK,CAAC;MACxBmC,SAAS,CAAC,CAAC;MACXrH,KAAK,CAACsH,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAqB,gBAAA,EAAAC,qBAAA;MAClBP,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAqB,gBAAA,GAALrB,KAAK,CAAEG,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBxB,IAAI,cAAAyB,qBAAA,uBAArBA,qBAAA,CAAuBL,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9C5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGnI,WAAW,CAACG,WAAW,CAACiI,MAAM,EAAE;IACrDpB,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCpH,KAAK,CAACsH,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAClB1G,KAAK,CAAC0G,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAIF;EACA,MAAMyB,iBAAiB,GAAGrI,WAAW,CAClCsI,SAAS,IAAKnI,WAAW,CAACoI,SAAS,CAACD,SAAS,CAAC,EAC/C;IACEtB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAMyB,MAAM,GAAGzB,QAAQ,CAACN,IAAI;MAC5B,IAAI+B,MAAM,CAACtG,MAAM,KAAK,SAAS,EAAE;QAC/BhC,KAAK,CAACsH,OAAO,CAAC,yCAAyCgB,MAAM,CAACC,aAAa,GAAG,CAAC;MACjF,CAAC,MAAM,IAAID,MAAM,CAACtG,MAAM,KAAK,UAAU,EAAE;QACvChC,KAAK,CAACwI,IAAI,CAAC,sCAAsC,CAAC;MACpD,CAAC,MAAM;QACLxI,KAAK,CAAC0G,KAAK,CAAC,sBAAsB4B,MAAM,CAACV,OAAO,EAAE,CAAC;MACrD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAA+B,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClBlB,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAI,CAAAA,KAAK,aAALA,KAAK,wBAAA+B,gBAAA,GAAL/B,KAAK,CAAEG,QAAQ,cAAA4B,gBAAA,uBAAfA,gBAAA,CAAiBzG,MAAM,MAAK,GAAG,EAAE;QACnChC,KAAK,CAAC4I,OAAO,CAAC,6EAA6E,CAAC;MAC9F,CAAC,MAAM,IAAI,CAAAlC,KAAK,aAALA,KAAK,wBAAAgC,gBAAA,GAALhC,KAAK,CAAEG,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBnC,IAAI,cAAAoC,qBAAA,uBAArBA,qBAAA,CAAuBf,OAAO,MAAK,oBAAoB,EAAE;QAClE5H,KAAK,CAAC4I,OAAO,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACL,MAAMtB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAmC,gBAAA,GAALnC,KAAK,CAAEG,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtC,IAAI,cAAAuC,qBAAA,uBAArBA,qBAAA,CAAuBnB,MAAM,MAC9BjB,KAAK,aAALA,KAAK,wBAAAqC,gBAAA,GAALrC,KAAK,CAAEG,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBxC,IAAI,cAAAyC,qBAAA,uBAArBA,qBAAA,CAAuBpB,OAAO,MAC9BlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mBAAmB;QACvC5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;MAC3B;IACF;EACF,CACF,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAGnJ,WAAW,CACrCsI,SAAS,IAAKnI,WAAW,CAACiJ,YAAY,CAACd,SAAS,CAAC,EAClD;IACEtB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAMyB,MAAM,GAAGzB,QAAQ,CAACN,IAAI;MAC5B,IAAI+B,MAAM,CAACtG,MAAM,KAAK,kBAAkB,EAAE;QACxChC,KAAK,CAACsH,OAAO,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM,IAAIgB,MAAM,CAACtG,MAAM,KAAK,YAAY,EAAE;QACzChC,KAAK,CAAC4I,OAAO,CAAC,4DAA4D,CAAC;MAC7E,CAAC,MAAM,IAAIN,MAAM,CAACtG,MAAM,KAAK,iBAAiB,EAAE;QAC9ChC,KAAK,CAAC4I,OAAO,CAAC,oDAAoD,CAAC;MACrE,CAAC,MAAM;QACL5I,KAAK,CAAC0G,KAAK,CAAC,4BAA4B4B,MAAM,CAACV,OAAO,EAAE,CAAC;MAC3D;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAyC,gBAAA,EAAAC,qBAAA;MAClB3B,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAyC,gBAAA,GAALzC,KAAK,CAAEG,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB5C,IAAI,cAAA6C,qBAAA,uBAArBA,qBAAA,CAAuBzB,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,yBAAyB;MAC7C5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAM2B,qBAAqB,GAAGvJ,WAAW,CACtCsI,SAAS,IAAKnI,WAAW,CAACqJ,aAAa,CAAClB,SAAS,CAAC,EACnD;IACEtB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAMyB,MAAM,GAAGzB,QAAQ,CAACN,IAAI;MAC5B,IAAI+B,MAAM,CAACtG,MAAM,KAAK,iBAAiB,IAAIsG,MAAM,CAACtG,MAAM,KAAK,kBAAkB,EAAE;QAC/EhC,KAAK,CAACsH,OAAO,CAAC,sFAAsF,CAAC;QACrG;QACA9B,oBAAoB,CAAC8C,MAAM,CAACiB,YAAY,IAAI,EAAE,CAAC;QAC/CjE,wBAAwB,CAAC,IAAI,CAAC;;QAE9B;QACAkE,uBAAuB,CAAClB,MAAM,CAACmB,UAAU,CAAC;MAC5C,CAAC,MAAM,IAAInB,MAAM,CAACtG,MAAM,KAAK,gBAAgB,EAAE;QAC7ChC,KAAK,CAACwI,IAAI,CAAC,gFAAgF,CAAC;QAC5FhD,oBAAoB,CAAC,CACnB,oCAAoC,EACpC,wDAAwD,EACxD,yCAAyC,CAC1C,CAAC;QACFF,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,MAAM;QACLtF,KAAK,CAAC0G,KAAK,CAAC,0BAA0B4B,MAAM,CAACV,OAAO,EAAE,CAAC;MACzD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA;MAClBlC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAgD,gBAAA,GAALhD,KAAK,CAAEG,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBnD,IAAI,cAAAoD,qBAAA,uBAArBA,qBAAA,CAAuBhC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,gCAAgC;MACpD5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMkC,6BAA6B,GAAG9J,WAAW,CAC/C,CAAC;IAAEsI,SAAS;IAAEyB;EAAa,CAAC,KAAK5J,WAAW,CAAC6J,qBAAqB,CAAC1B,SAAS,EAAEyB,YAAY,CAAC,EAC3F;IACE/C,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAMyB,MAAM,GAAGzB,QAAQ,CAACN,IAAI;MAC5B,IAAI+B,MAAM,CAACtG,MAAM,KAAK,gBAAgB,EAAE;QACtChC,KAAK,CAACsH,OAAO,CAAC,sCAAsCgB,MAAM,CAACyB,YAAY,iBAAiB,CAAC;QACzF1D,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MAC3C,CAAC,MAAM;QACLpH,KAAK,CAAC0G,KAAK,CAAC,6BAA6B4B,MAAM,CAACV,OAAO,EAAE,CAAC;MAC5D;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAsD,gBAAA,EAAAC,qBAAA;MAClBxC,OAAO,CAACf,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAsD,gBAAA,GAALtD,KAAK,CAAEG,QAAQ,cAAAmD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzD,IAAI,cAAA0D,qBAAA,uBAArBA,qBAAA,CAAuBtC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mCAAmC;MACvD5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMwC,8BAA8B,GAAGpK,WAAW,CAC/CsI,SAAS,IAAKnI,WAAW,CAACkK,sBAAsB,CAAC/B,SAAS,CAAC,EAC5D;IACEtB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAMyB,MAAM,GAAGzB,QAAQ,CAACN,IAAI;MAC5BvG,KAAK,CAACsH,OAAO,CAAC,0CAA0C,CAAC;MACzD8C,sBAAsB,CAAC9B,MAAM,CAACmB,UAAU,CAAC;MACzCnE,wBAAwB,CAAC,KAAK,CAAC;IACjC,CAAC;IACD2B,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MAClB7C,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAA2D,gBAAA,GAAL3D,KAAK,CAAEG,QAAQ,cAAAwD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB9D,IAAI,cAAA+D,qBAAA,uBAArBA,qBAAA,CAAuB3C,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,qCAAqC;MACzD5H,KAAK,CAAC0G,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMV,QAAQ,GAAGuD,KAAK,CAACC,OAAO,CAAChE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEQ,QAAQ,CAAC,GAClDR,YAAY,CAACQ,QAAQ,GACrBuD,KAAK,CAACC,OAAO,CAAChE,YAAY,CAAC,GAC3BA,YAAY,GACZ,EAAE;EAEN,MAAMa,SAAS,GAAGA,CAAA,KAAM;IACtBzB,WAAW,CAAC;MACVhD,IAAI,EAAE,EAAE;MACRiD,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9BhC,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjB+B,aAAa,EAAE,KAAK;MACpBhC,UAAU,EAAE,UAAU;MACtBiC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFhB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqF,YAAY,GAAGA,CAAA,KAAM;IACzBzF,mBAAmB,CAAC,IAAI,CAAC;IACzBqC,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMqD,UAAU,GAAIlK,OAAO,IAAK;IAC9B4E,kBAAkB,CAAC5E,OAAO,CAAC;IAE3BoF,WAAW,CAAC;MACVhD,IAAI,EAAEpC,OAAO,CAACoC,IAAI,IAAI,EAAE;MACxBiD,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAEtF,OAAO,CAACsF,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAEvF,OAAO,CAACuF,iBAAiB,IAAI,WAAW;MAC3DhC,QAAQ,EAAEvD,OAAO,CAACuD,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAEzD,OAAO,CAACyD,QAAQ,IAAI,OAAO;MACrC+B,aAAa,EAAExF,OAAO,CAACwD,UAAU,IAAIxD,OAAO,CAACwD,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAExD,OAAO,CAACwD,UAAU,IAAI,UAAU;MAC5CiC,UAAU,EAAEzF,OAAO,CAACyF,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAE1F,OAAO,CAAC0F,UAAU,GAAG1F,OAAO,CAAC0F,UAAU,CAACyE,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnExE,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFlB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0F,YAAY,GAAIpK,OAAO,IAAK;IAChC,IAAIqK,MAAM,CAACC,OAAO,CAAC,4CAA4CtK,OAAO,CAACoC,IAAI,IAAI,CAAC,EAAE;MAChFqF,cAAc,CAAC8C,MAAM,CAACvK,OAAO,CAACqB,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAMmJ,UAAU,GAAIxK,OAAO,IAAK;IAC9B;IACA,MAAMyK,SAAS,GAAGzK,OAAO,CAACwD,UAAU,IAAIxD,OAAO,CAACwD,UAAU,KAAK,UAAU,GACrE,GAAGxD,OAAO,CAACwD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAAGtC,OAAO,CAACyF,UAAU,GAAG,KAAKzF,OAAO,CAACyF,UAAU,IAAIzF,OAAO,CAAC0F,UAAU,GAAG,GAAG,EAAE,EAAE,GAClH,UAAU;;IAEd;IACA,MAAMgF,WAAW,GAAG,CAClB,YAAY1K,OAAO,CAACoC,IAAI,EAAE,EAC1B,WAAWpC,OAAO,CAACwB,MAAM,EAAE,EAC3B,UAAUiJ,SAAS,EAAE,EACrB,aAAazK,OAAO,CAACyD,QAAQ,IAAI,OAAO,EAAE,EAC1C,aAAazD,OAAO,CAACuD,QAAQ,IAAI,KAAK,EAAE,EACxC,YAAY,IAAIoH,IAAI,CAAC3K,OAAO,CAAC4K,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAChE,CAACC,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACAtL,KAAK,CAACsH,OAAO,CAAC,yBAAyB4D,WAAW,EAAE,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;;IAEzE;IACA,IAAI/K,OAAO,CAACwD,UAAU,IAAIxD,OAAO,CAACwD,UAAU,KAAK,UAAU,IAAIxD,OAAO,CAACyF,UAAU,EAAE;MACjFuF,UAAU,CAAC,MAAM;QACfrD,iBAAiB,CAAC4C,MAAM,CAACvK,OAAO,CAACqB,EAAE,CAAC;MACtC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM4J,WAAW,GAAIjL,OAAO,IAAK;IAC/ByI,oBAAoB,CAAC8B,MAAM,CAACvK,OAAO,CAACqB,EAAE,CAAC;EACzC,CAAC;EAED,MAAM6J,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAGhG;IAAS,CAAC;;IAElC;IACA,IAAIgG,UAAU,CAAC1F,UAAU,KAAK,EAAE,EAAE0F,UAAU,CAAC1F,UAAU,GAAG,IAAI;IAC9D,IAAI0F,UAAU,CAACzF,UAAU,KAAK,EAAE,EAAEyF,UAAU,CAACzF,UAAU,GAAG,IAAI;IAC9D,IAAIyF,UAAU,CAACxF,cAAc,KAAK,EAAE,EAAEwF,UAAU,CAACxF,cAAc,GAAG,IAAI;IACtE,IAAIwF,UAAU,CAACvF,cAAc,KAAK,EAAE,EAAEuF,UAAU,CAACvF,cAAc,GAAG,IAAI;IACtE,IAAIuF,UAAU,CAAC7F,UAAU,KAAK,EAAE,EAAE6F,UAAU,CAAC7F,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAI6F,UAAU,CAACzF,UAAU,EAAE;MACzB,MAAM0F,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAACzF,UAAU,CAAC;MAC5C,IAAI4F,KAAK,CAACF,IAAI,CAAC,EAAE;QACf5L,KAAK,CAAC0G,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACAiF,UAAU,CAACzF,UAAU,GAAG0F,IAAI;IAC9B;;IAEA;IACA,IAAIjG,QAAQ,CAAC3B,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAAC2B,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChDlG,KAAK,CAAC0G,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAMkF,IAAI,GAAGC,QAAQ,CAAClG,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAI4F,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3C5L,KAAK,CAAC0G,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAMqF,SAAS,GAAG;MAChBnJ,IAAI,EAAE+I,UAAU,CAAC/I,IAAI;MACrBkD,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;MACjCkG,cAAc,EAAE;QACdnG,YAAY,EAAE8F,UAAU,CAAC9F,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAE4F,UAAU,CAAC5F,iBAAiB,IAAI,WAAW;QAC9DhC,QAAQ,EAAE4H,UAAU,CAAC5H,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAE0H,UAAU,CAAC1H,QAAQ,IAAI;MACnC,CAAC;MACDgI,YAAY,EAAEN,UAAU,CAAC3H,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAE2H,UAAU,CAAC3H,UAAU;QACjCkI,IAAI,EAAEP,UAAU,CAAC1F,UAAU;QAC3B2F,IAAI,EAAED,UAAU,CAACzF,UAAU;QAC3BiG,QAAQ,EAAER,UAAU,CAACxF,cAAc;QACnCiG,QAAQ,EAAET,UAAU,CAACvF;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIjB,eAAe,EAAE;MACnB0C,cAAc,CAACkD,MAAM,CAAC;QAAElJ,EAAE,EAAEsD,eAAe,CAACtD,EAAE;QAAE0E,IAAI,EAAEwF;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACL7E,cAAc,CAAC6D,MAAM,CAACgB,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC3G,WAAW,CAAC4G,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,mBAAmB,GAAIjM,OAAO,IAAK;IACvC6I,qBAAqB,CAAC0B,MAAM,CAACvK,OAAO,CAACqB,EAAE,CAAC;EAC1C,CAAC;EAED,MAAM6K,4BAA4B,GAAIlM,OAAO,IAAK;IAChD,IAAIqK,MAAM,CAACC,OAAO,CAAC,+DAA+DtK,OAAO,CAACoC,IAAI,IAAI,CAAC,EAAE;MACnGsH,8BAA8B,CAACa,MAAM,CAACvK,OAAO,CAACqB,EAAE,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAM2H,uBAAuB,GAAIpB,SAAS,IAAK;IAC7C;IACAgC,sBAAsB,CAAChC,SAAS,CAAC;IAEjC,MAAMuE,UAAU,GAAGC,WAAW,CAAC,YAAY;MACzC,IAAI;QACF,MAAM/F,QAAQ,GAAG,MAAM5G,WAAW,CAAC4M,mBAAmB,CAACzE,SAAS,CAAC;QACjE,MAAMpG,MAAM,GAAG6E,QAAQ,CAACN,IAAI,CAACuG,YAAY;QAEzC,IAAI9K,MAAM,CAACA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,CAACA,MAAM,KAAK,SAAS,EAAE;UACrEhC,KAAK,CAAC4I,OAAO,CAAC,sDAAsD,CAAC;UACrEwB,sBAAsB,CAAChC,SAAS,CAAC;QACnC,CAAC,MAAM,IAAIpG,MAAM,CAACA,MAAM,KAAK,YAAY,EAAE;UACzCoI,sBAAsB,CAAChC,SAAS,CAAC;QACnC;QACA;MACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEXjB,qBAAqB,CAAC+G,IAAI,IAAI,IAAI9G,GAAG,CAAC8G,IAAI,CAACO,GAAG,CAAC3E,SAAS,EAAEuE,UAAU,CAAC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMvC,sBAAsB,GAAIhC,SAAS,IAAK;IAC5C,MAAMuE,UAAU,GAAG3L,kBAAkB,CAACgM,GAAG,CAAC5E,SAAS,CAAC;IACpD,IAAIuE,UAAU,EAAE;MACdM,aAAa,CAACN,UAAU,CAAC;MACzBlH,qBAAqB,CAAC+G,IAAI,IAAI;QAC5B,MAAMU,MAAM,GAAG,IAAIxH,GAAG,CAAC8G,IAAI,CAAC;QAC5BU,MAAM,CAAChF,MAAM,CAACE,SAAS,CAAC;QACxB,OAAO8E,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA9Q,KAAK,CAAC+Q,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXnM,kBAAkB,CAACoM,OAAO,CAAET,UAAU,IAAK;QACzCM,aAAa,CAACN,UAAU,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC3L,kBAAkB,CAAC,CAAC;EAExB,MAAMqM,2BAA2B,GAAG,MAAO7M,OAAO,IAAK;IACrD;IACA4J,sBAAsB,CAAC5J,OAAO,CAACqB,EAAE,CAAC;IAElC,IAAI;MACF;MACA,MAAMyL,cAAc,GAAG,MAAMrN,WAAW,CAACsN,iBAAiB,CAAC/M,OAAO,CAACqB,EAAE,CAAC;MACtE,MAAMgI,YAAY,GAAGyD,cAAc,CAAC/G,IAAI,CAACiH,aAAa,IAAI,CAAC,CAAC;;MAE5D;MACA5D,6BAA6B,CAACmB,MAAM,CAAC;QACnC3C,SAAS,EAAE5H,OAAO,CAACqB,EAAE;QACrBgI,YAAY,EAAE;UACZ4D,KAAK,EAAE5D,YAAY,CAAC4D,KAAK,IAAI,kBAAkB;UAAE;UACjDtB,QAAQ,EAAEtC,YAAY,CAACsC,QAAQ,IAAI,eAAe;UAClDuB,OAAO,EAAE7D,YAAY,CAAC6D,OAAO,IAAI;QACnC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhH,KAAK,EAAE;MACde,OAAO,CAACkG,GAAG,CAAC,kEAAkE,CAAC;MAC/E;MACA/D,6BAA6B,CAACmB,MAAM,CAAC;QACnC3C,SAAS,EAAE5H,OAAO,CAACqB,EAAE;QACrBgI,YAAY,EAAE;UACZ4D,KAAK,EAAE,kBAAkB;UAAE;UAC3BtB,QAAQ,EAAE,eAAe;UACzBuB,OAAO,EAAE;QACX;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAIhH,KAAK,EAAE;IACT,oBACEtG,OAAA,CAAC9D,GAAG;MAACsR,SAAS,EAAC,SAAS;MAAAxL,QAAA,eACtBhC,OAAA,CAACxC,KAAK;QAACiQ,QAAQ,EAAC,OAAO;QAAC3L,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACE9C,OAAA,CAAC9D,GAAG;IAACsR,SAAS,EAAC,SAAS;IAAAxL,QAAA,gBACtBhC,OAAA,CAAC9D,GAAG;MAAC4F,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFhC,OAAA,CAAC7D,UAAU;QAAC4G,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9C,OAAA,CAAC9D,GAAG;QAAC4F,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnChC,OAAA,CAAC1D,MAAM;UACLyG,OAAO,EAAC,UAAU;UAClBkB,SAAS,eAAEjE,OAAA,CAACxB,WAAW;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAM6C,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAE;UACzDlF,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAAC1D,MAAM;UACLyG,OAAO,EAAC,WAAW;UACnBkB,SAAS,eAAEjE,OAAA,CAACpC,OAAO;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEiH,YAAa;UACtBvI,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELuD,SAAS,iBAAIrG,OAAA,CAACvC,cAAc;MAACqE,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAI9C8D,QAAQ,CAAC8G,MAAM,KAAK,CAAC,IAAI,CAACrH,SAAS,gBAClCrG,OAAA,CAAC5D,IAAI;MAAA4F,QAAA,eACHhC,OAAA,CAAC3D,WAAW;QAACyF,EAAE,EAAE;UAAE6L,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA5L,QAAA,gBAC9ChC,OAAA,CAAClC,UAAU;UAACgE,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpE9C,OAAA,CAAC7D,UAAU;UAAC4G,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9C,OAAA,CAAC7D,UAAU;UAAC4G,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9C,OAAA,CAAC1D,MAAM;UACLyG,OAAO,EAAC,WAAW;UACnBkB,SAAS,eAAEjE,OAAA,CAACpC,OAAO;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEiH,YAAa;UACtBvI,EAAE,EAAE;YAAEoC,aAAa,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEP9C,OAAA,CAACzD,IAAI;MAAC+G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxB4E,QAAQ,CAACiH,GAAG,CAAEzN,OAAO,iBACpBJ,OAAA,CAACzD,IAAI;QAACiH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACqK,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/L,QAAA,eAC9BhC,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAEiK,UAAW;UACnBhK,QAAQ,EAAEkK,YAAa;UACvBjK,MAAM,EAAEqK,UAAW;UACnBpK,OAAO,EAAE6K,WAAY;UACrB5K,eAAe,EAAE4L,mBAAoB;UACrC3L,uBAAuB,EAAEuM,2BAA4B;UACrDtM,wBAAwB,EAAE2L,4BAA6B;UACvD1L,kBAAkB,EAAEA;QAAmB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC,GAXkC1C,OAAO,CAACqB,EAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAY1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGD9C,OAAA,CAACtD,MAAM;MACL2H,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxBmC,SAAS,CAAC,CAAC;MACb,CAAE;MACF+G,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAjM,QAAA,gBAEThC,OAAA,CAACrD,WAAW;QAAAqF,QAAA,EACT+C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACd9C,OAAA,CAACpD,aAAa;QAAAoF,QAAA,eACZhC,OAAA,CAAC9D,GAAG;UAAC4F,EAAE,EAAE;YAAEoM,EAAE,EAAE;UAAE,CAAE;UAAAlM,QAAA,eACjBhC,OAAA,CAACzD,IAAI;YAAC+G,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzBhC,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBhC,OAAA,CAAClD,SAAS;gBACRmR,SAAS;gBACThL,KAAK,EAAC,cAAc;gBACpBkJ,KAAK,EAAE5G,QAAQ,CAAC/C,IAAK;gBACrB2L,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,MAAM,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;gBAC1DmC,QAAQ;cAAA;gBAAA3L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACqK,EAAE,EAAE,CAAE;cAAA9L,QAAA,eACvBhC,OAAA,CAACjD,WAAW;gBAACkR,SAAS;gBAAAjM,QAAA,gBACpBhC,OAAA,CAAChD,UAAU;kBAAAgF,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC9C,OAAA,CAAC/C,MAAM;kBACLkP,KAAK,EAAE5G,QAAQ,CAACE,YAAa;kBAC7B0I,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,cAAc,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBAClElJ,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpBhC,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,QAAQ;oBAAAnK,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,SAAS;oBAAAnK,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,MAAM;oBAAAnK,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACqK,EAAE,EAAE,CAAE;cAAA9L,QAAA,eACvBhC,OAAA,CAACjD,WAAW;gBAACkR,SAAS;gBAAAjM,QAAA,gBACpBhC,OAAA,CAAChD,UAAU;kBAAAgF,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1C9C,OAAA,CAAC/C,MAAM;kBACLkP,KAAK,EAAE5G,QAAQ,CAACI,iBAAkB;kBAClCwI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,mBAAmB,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBACvElJ,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzBhC,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,WAAW;oBAAAnK,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChD9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,UAAU;oBAAAnK,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,UAAU;oBAAAnK,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,UAAU;oBAAAnK,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACqK,EAAE,EAAE,CAAE;cAAA9L,QAAA,eACvBhC,OAAA,CAACjD,WAAW;gBAACkR,SAAS;gBAAAjM,QAAA,gBACpBhC,OAAA,CAAChD,UAAU;kBAAAgF,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjC9C,OAAA,CAAC/C,MAAM;kBACLkP,KAAK,EAAE5G,QAAQ,CAAC5B,QAAS;kBACzBwK,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,UAAU,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBAC9DlJ,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBhC,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,KAAK;oBAAAnK,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpC9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,kBAAkB;oBAAAnK,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9D9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,kBAAkB;oBAAAnK,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9D9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,eAAe;oBAAAnK,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACqK,EAAE,EAAE,CAAE;cAAA9L,QAAA,eACvBhC,OAAA,CAACjD,WAAW;gBAACkR,SAAS;gBAAAjM,QAAA,gBACpBhC,OAAA,CAAChD,UAAU;kBAAAgF,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjC9C,OAAA,CAAC/C,MAAM;kBACLkP,KAAK,EAAE5G,QAAQ,CAAC1B,QAAS;kBACzBsK,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,UAAU,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBAC9DlJ,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBhC,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,OAAO;oBAAAnK,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,OAAO;oBAAAnK,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7C9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,OAAO;oBAAAnK,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvD9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,OAAO;oBAAAnK,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBhC,OAAA,CAAClD,SAAS;gBACRmR,SAAS;gBACThL,KAAK,EAAC,uBAAuB;gBAC7BkJ,KAAK,EAAE5G,QAAQ,CAACG,UAAW;gBAC3ByI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,YAAY,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;gBAChEoC,WAAW,EAAC;cAAsC;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBhC,OAAA,CAAC7D,UAAU;gBAAC4G,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEP9C,OAAA,CAACzD,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACqK,EAAE,EAAE,CAAE;cAAA9L,QAAA,eACvBhC,OAAA,CAACjD,WAAW;gBAACkR,SAAS;gBAAAjM,QAAA,gBACpBhC,OAAA,CAAChD,UAAU;kBAAAgF,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC9C,OAAA,CAAC/C,MAAM;kBACLkP,KAAK,EAAE5G,QAAQ,CAAC3B,UAAW;kBAC3BuK,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAAClC,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAEuC,SAAS,CAAC;oBACzCvC,gBAAgB,CAAC,eAAe,EAAEuC,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACFvL,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElBhC,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,UAAU;oBAAAnK,QAAA,eACxBhC,OAAA,CAAC9D,GAAG;sBAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjE9C,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,MAAM;oBAAAnK,QAAA,eACpBhC,OAAA,CAAC9D,GAAG;sBAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7C9C,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,OAAO;oBAAAnK,QAAA,eACrBhC,OAAA,CAAC9D,GAAG;sBAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9C9C,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,QAAQ;oBAAAnK,QAAA,eACtBhC,OAAA,CAAC9D,GAAG;sBAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/C9C,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACX9C,OAAA,CAAC9C,QAAQ;oBAACiP,KAAK,EAAC,KAAK;oBAAAnK,QAAA,eACnBhC,OAAA,CAAC9D,GAAG;sBAAA8F,QAAA,gBACFhC,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5C9C,OAAA,CAAC7D,UAAU;wBAAC4G,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACT9C,OAAA,CAACtC,cAAc;kBAAAsE,QAAA,GACZuD,QAAQ,CAAC3B,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/D2B,QAAQ,CAAC3B,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnE2B,QAAQ,CAAC3B,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzE2B,QAAQ,CAAC3B,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5E2B,QAAQ,CAAC3B,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENyC,QAAQ,CAAC3B,UAAU,KAAK,UAAU,iBACjC5D,OAAA,CAAAE,SAAA;cAAA8B,QAAA,gBACEhC,OAAA,CAACzD,IAAI;gBAACiH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqK,EAAE,EAAE,CAAE;gBAAA9L,QAAA,eACvBhC,OAAA,CAAClD,SAAS;kBACRmR,SAAS;kBACThL,KAAK,EAAC,YAAY;kBAClBkJ,KAAK,EAAE5G,QAAQ,CAACM,UAAW;kBAC3BsI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,YAAY,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBAChEmC,QAAQ;kBACRC,WAAW,EACThJ,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACD6K,UAAU,EAAC;gBAA+C;kBAAA9L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP9C,OAAA,CAACzD,IAAI;gBAACiH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqK,EAAE,EAAE,CAAE;gBAAA9L,QAAA,eACvBhC,OAAA,CAAClD,SAAS;kBACRmR,SAAS;kBACThL,KAAK,EAAC,YAAY;kBAClBkJ,KAAK,EAAE5G,QAAQ,CAACO,UAAW;kBAC3BqI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,YAAY,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBAChEuC,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACThJ,QAAQ,CAAC3B,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnD2B,QAAQ,CAAC3B,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnD2B,QAAQ,CAAC3B,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrD2B,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACD6K,UAAU,EAAE,UAAUlJ,QAAQ,CAAC3B,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChEiM,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAAlM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP9C,OAAA,CAACzD,IAAI;gBAACiH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqK,EAAE,EAAE,CAAE;gBAAA9L,QAAA,eACvBhC,OAAA,CAAClD,SAAS;kBACRmR,SAAS;kBACThL,KAAK,EAAEsC,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEuI,KAAK,EAAE5G,QAAQ,CAACQ,cAAe;kBAC/BoI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,gBAAgB,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBACpEoC,WAAW,EACThJ,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACD6K,UAAU,EACRlJ,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP9C,OAAA,CAACzD,IAAI;gBAACiH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqK,EAAE,EAAE,CAAE;gBAAA9L,QAAA,eACvBhC,OAAA,CAAClD,SAAS;kBACRmR,SAAS;kBACThL,KAAK,EAAEsC,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEuI,KAAK,EAAE5G,QAAQ,CAACS,cAAe;kBAC/BmI,QAAQ,EAAGC,CAAC,IAAKnC,gBAAgB,CAAC,gBAAgB,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;kBACpEuC,IAAI,EAAC,UAAU;kBACfH,WAAW,EACThJ,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACD6K,UAAU,EACRlJ,QAAQ,CAAC3B,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP9C,OAAA,CAACzD,IAAI;gBAACiH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChBhC,OAAA,CAAC9D,GAAG;kBAAC4F,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzDhC,OAAA,CAAC1D,MAAM;oBACLyG,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAI2B,eAAe,EAAE;wBACnBgD,iBAAiB,CAAC4C,MAAM,CAAC5F,eAAe,CAACtD,EAAE,CAAC;sBAC9C,CAAC,MAAM;wBACL;wBACA,MAAMoJ,SAAS,GAAG,GAAGtF,QAAQ,CAAC3B,UAAU,CAAClB,WAAW,CAAC,CAAC,WAAW6C,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,UAAU,EAAE;wBAC7G,MAAMgJ,QAAQ,GAAGvJ,QAAQ,CAACQ,cAAc,GAAG,WAAWR,QAAQ,CAACQ,cAAc,GAAG,GAAG,YAAY;wBAC/FnG,KAAK,CAACwI,IAAI,CAAC,yBAAyByC,SAAS,GAAGiE,QAAQ,4CAA4C,EAAE;0BAAE3D,QAAQ,EAAE;wBAAK,CAAC,CAAC;sBAC3H;oBACF,CAAE;oBACFhH,QAAQ,EAAE,CAACoB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAIiC,iBAAiB,CAAC1B,SAAU;oBACtFvE,EAAE,EAAE;sBAAEoC,aAAa,EAAE;oBAAO,CAAE;oBAAAlC,QAAA,EAE7B+F,iBAAiB,CAAC1B,SAAS,GAAG,YAAY,GAAGtB,eAAe,GAAG,iBAAiB,GAAG;kBAAgB;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACT9C,OAAA,CAAC7D,UAAU;oBAAC4G,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB9C,OAAA,CAACnD,aAAa;QAAAmF,QAAA,gBACZhC,OAAA,CAAC1D,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAM;YACbwB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxBmC,SAAS,CAAC,CAAC;UACb,CAAE;UAAAjF,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAAC1D,MAAM;UACLyG,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAEkI,YAAa;UACtBnH,QAAQ,EAAE,CAACoB,QAAQ,CAAC/C,IAAI,IAAIsE,cAAc,CAACT,SAAS,IAAIoB,cAAc,CAACpB,SAAU;UAAArE,QAAA,EAEhF+C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9C,OAAA,CAACtD,MAAM;MACL2H,IAAI,EAAEY,qBAAsB;MAC5BV,OAAO,EAAEA,CAAA,KAAMW,wBAAwB,CAAC,KAAK,CAAE;MAC/C8I,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAjM,QAAA,gBAEThC,OAAA,CAACrD,WAAW;QAACmF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE4B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACjEhC,OAAA,CAACd,YAAY;UAAC4C,EAAE,EAAE;YAAEqB,KAAK,EAAE;UAAU;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd9C,OAAA,CAACpD,aAAa;QAAAoF,QAAA,gBACZhC,OAAA,CAACxC,KAAK;UAACiQ,QAAQ,EAAC,MAAM;UAAC3L,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAEtC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAER9C,OAAA,CAAC9D,GAAG;UAAC6S,SAAS,EAAC,IAAI;UAACjN,EAAE,EAAE;YAAEkN,EAAE,EAAE;UAAE,CAAE;UAAAhN,QAAA,EAC/BmD,iBAAiB,CAAC0I,GAAG,CAAC,CAACoB,WAAW,EAAEC,KAAK,kBACxClP,OAAA,CAAC9D,GAAG;YAAC6S,SAAS,EAAC,IAAI;YAAajN,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eAC5ChC,OAAA,CAAC7D,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAAAf,QAAA,EAAEiN;YAAW;cAAAtM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC,GAD/BoM,KAAK;YAAAvM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEzB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA,CAACxC,KAAK;UAACiQ,QAAQ,EAAC,SAAS;UAAC3L,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,eACtChC,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAAAf,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAQ;YAAU;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+KAG7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAER9C,OAAA,CAACxC,KAAK;UAACiQ,QAAQ,EAAC,SAAS;UAAC3L,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,eACtChC,OAAA,CAAC7D,UAAU;YAAC4G,OAAO,EAAC,OAAO;YAAAf,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAQ;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mJAEvB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChB9C,OAAA,CAACnD,aAAa;QAAAmF,QAAA,gBACZhC,OAAA,CAAC1D,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAM8B,wBAAwB,CAAC,KAAK,CAAE;UAC/C/B,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAChB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAAC1D,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,MAAM+L,aAAa,GAAGvI,QAAQ,CAACwI,IAAI,CAACC,CAAC,IAAIzO,kBAAkB,CAACY,GAAG,CAAC6N,CAAC,CAAC5N,EAAE,CAAC,CAAC;YACtE,IAAI0N,aAAa,EAAE;cACjB7C,4BAA4B,CAAC6C,aAAa,CAAC;YAC7C;UACF,CAAE;UACFhM,KAAK,EAAC,SAAS;UACfJ,OAAO,EAAC,UAAU;UAAAf,QAAA,EACnB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAC4B,GAAA,CAt4BQD,QAAQ;EAAA,QAsBK9E,cAAc,EACVG,MAAM,EAGmBL,QAAQ,EAgBlCC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAaRA,WAAW,EAiCRA,WAAW,EA0BVA,WAAW,EAmCHA,WAAW,EAsBVA,WAAW;AAAA;AAAA4P,GAAA,GAhN3C7K,QAAQ;AAw4BjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAA8K,GAAA;AAAAC,YAAA,CAAA/K,EAAA;AAAA+K,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}