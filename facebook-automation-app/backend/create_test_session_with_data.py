#!/usr/bin/env python3
"""
Create a test session with mock scraped data for testing export functionality
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.database import AsyncSessionLocal
from app.models.scraped_data import ScrapingSession, ScrapedUser, ScrapingType
from sqlalchemy import select

async def create_test_session_with_data():
    """Create a test session with mock scraped data"""
    async with AsyncSessionLocal() as db:
        # Create test session
        session = ScrapingSession(
            name="Test Session with Data",
            post_url="https://www.facebook.com/groups/591054007361950/posts/test123",
            scraping_types="comments",
            status="completed",
            total_found=10,
            unique_users=10,
            progress_percentage=100.0,
            current_step="Completed",
            started_at=datetime.utcnow(),
            completed_at=datetime.utcnow()
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        print(f"✅ Created test session: ID={session.id}, Name={session.name}")
        
        # Add mock scraped users
        mock_users = [
            {"uid": "1235715216", "name": "<PERSON> Doe"},
            {"uid": "618584424608908", "name": "Jane Smith"},
            {"uid": "100095231620151", "name": "Bob Johnson"},
            {"uid": "115940658764963", "name": "Alice Brown"},
            {"uid": "125060567243092", "name": "Charlie Wilson"},
            {"uid": "533268069866604", "name": "Diana Davis"},
            {"uid": "100026030760358", "name": "Eva Martinez"},
            {"uid": "3597727420466696", "name": "Frank Garcia"},
            {"uid": "100010612569826", "name": "Grace Lee"},
            {"uid": "9819363271446884", "name": "Henry Taylor"}
        ]
        
        for user_data in mock_users:
            scraped_user = ScrapedUser(
                session_id=session.id,
                uid=user_data["uid"],
                name=user_data["name"],
                profile_url=f"https://facebook.com/{user_data['uid']}",
                interaction_type=ScrapingType.COMMENTS,
                scraped_at=datetime.utcnow()
            )
            db.add(scraped_user)
        
        await db.commit()
        print(f"✅ Added {len(mock_users)} mock users to session {session.id}")
        
        return session.id

async def main():
    """Main function"""
    print("🧪 Creating test session with mock data...")
    session_id = await create_test_session_with_data()
    print(f"🎯 Test session created with ID: {session_id}")
    print(f"🔗 Test export: curl 'http://localhost:8000/api/scraping/sessions/{session_id}/export?format=json'")

if __name__ == "__main__":
    asyncio.run(main())
