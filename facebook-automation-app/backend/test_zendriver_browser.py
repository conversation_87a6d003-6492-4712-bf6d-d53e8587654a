#!/usr/bin/env python3
"""
Test zendriver browser launch directly
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Try different zendriver imports
try:
    import zendriver as zd
    print("✅ Using system zendriver")
except ImportError:
    try:
        sys.path.insert(0, str(backend_dir / "zendriver_local"))
        import zendriver as zd
        print("✅ Using local zendriver")
    except ImportError:
        print("❌ Could not import zendriver")
        sys.exit(1)

async def test_basic_browser_launch():
    """Test basic browser launch with minimal config"""
    print("🧪 Testing basic browser launch...")
    
    try:
        # Try with minimal config
        browser = await zd.start(
            headless=False,
            sandbox=False,
            browser_connection_timeout=10.0,
            browser_connection_max_tries=5
        )
        
        print("✅ Browser launched successfully!")
        
        # Test basic navigation
        page = await browser.get("https://www.google.com")
        title = await page.title
        print(f"✅ Page title: {title}")
        
        await browser.stop()
        print("✅ <PERSON>rowser stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ <PERSON>rowser launch failed: {str(e)}")
        return False

async def test_browser_with_explicit_path():
    """Test browser launch with explicit Chrome path"""
    print("🧪 Testing browser launch with explicit Chrome path...")
    
    chrome_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/opt/google/chrome/chrome"
    ]
    
    for chrome_path in chrome_paths:
        if os.path.exists(chrome_path):
            print(f"🔍 Found Chrome at: {chrome_path}")
            
            try:
                browser = await zd.start(
                    browser_executable_path=chrome_path,
                    headless=False,
                    sandbox=False,
                    browser_connection_timeout=10.0,
                    browser_connection_max_tries=5
                )
                
                print("✅ Browser launched with explicit path!")
                
                await browser.stop()
                print("✅ Browser stopped successfully")
                
                return True
                
            except Exception as e:
                print(f"❌ Failed with path {chrome_path}: {str(e)}")
                continue
    
    print("❌ No working Chrome path found")
    return False

async def test_browser_with_profile():
    """Test browser launch with user profile"""
    print("🧪 Testing browser launch with user profile...")
    
    try:
        # Create temp profile directory
        profile_dir = Path("temp_profile_test")
        profile_dir.mkdir(exist_ok=True)
        
        browser = await zd.start(
            user_data_dir=str(profile_dir),
            headless=False,
            sandbox=False,
            browser_connection_timeout=10.0,
            browser_connection_max_tries=5
        )
        
        print("✅ Browser launched with profile!")
        
        await browser.stop()
        print("✅ Browser stopped successfully")
        
        # Cleanup
        import shutil
        shutil.rmtree(profile_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Browser launch with profile failed: {str(e)}")
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Zendriver Browser Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Launch", test_basic_browser_launch),
        ("Explicit Path", test_browser_with_explicit_path),
        ("With Profile", test_browser_with_profile)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {str(e)}")
            results.append((test_name, False))
    
    print("\n🏁 Test Results Summary")
    print("=" * 50)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    successful_tests = sum(1 for _, success in results if success)
    print(f"\n📊 {successful_tests}/{len(results)} tests passed")

if __name__ == "__main__":
    asyncio.run(main())
