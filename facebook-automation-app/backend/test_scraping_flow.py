#!/usr/bin/env python3
"""
Test script to verify the complete scraping flow:
1. Create session
2. Start scraping
3. Monitor progress
4. Export results
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_scraping_flow():
    """Test the complete scraping flow"""
    async with httpx.AsyncClient() as client:
        print("🧪 Testing Complete Scraping Flow")
        print("=" * 50)
        
        # Step 1: Create scraping session
        print("\n1️⃣ Creating scraping session...")
        create_data = {
            "name": f"Test Session {datetime.now().strftime('%H:%M:%S')}",
            "post_url": "https://www.facebook.com/groups/591054007361950/posts/1234567890",
            "scraping_types": ["comments"],
            "max_users": 100
        }
        
        try:
            response = await client.post(f"{BASE_URL}/api/scraping/sessions", json=create_data)
            if response.status_code == 201:
                session_data = response.json()
                session_id = session_data["id"]
                print(f"✅ Session created successfully: ID={session_id}, Status={session_data['status']}")
            else:
                print(f"❌ Failed to create session: {response.status_code} - {response.text}")
                return
        except Exception as e:
            print(f"❌ Error creating session: {e}")
            return
        
        # Step 2: Monitor session progress
        print(f"\n2️⃣ Monitoring session {session_id} progress...")
        max_wait_time = 60  # Wait up to 60 seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = await client.get(f"{BASE_URL}/api/scraping/sessions/{session_id}")
                if response.status_code == 200:
                    session = response.json()
                    status = session.get("status", "unknown")
                    progress = session.get("progress_percentage", 0)
                    current_step = session.get("current_step", "")
                    
                    print(f"📊 Status: {status}, Progress: {progress}%, Step: {current_step}")
                    
                    if status == "completed":
                        print(f"✅ Session completed! Found {session.get('total_found', 0)} users")
                        break
                    elif status in ["error", "failed"]:
                        print(f"❌ Session failed: {session.get('error_message', 'Unknown error')}")
                        break
                    
                    await asyncio.sleep(3)  # Wait 3 seconds before next check
                else:
                    print(f"❌ Failed to get session status: {response.status_code}")
                    break
            except Exception as e:
                print(f"❌ Error monitoring session: {e}")
                break
        else:
            print("⏰ Timeout waiting for session completion")
        
        # Step 3: Get final session status
        print(f"\n3️⃣ Getting final session status...")
        try:
            response = await client.get(f"{BASE_URL}/api/scraping/sessions/{session_id}")
            if response.status_code == 200:
                session = response.json()
                print(f"📋 Final Status: {session.get('status')}")
                print(f"📋 Total Found: {session.get('total_found', 0)}")
                print(f"📋 Unique Users: {session.get('unique_users', 0)}")
                print(f"📋 Progress: {session.get('progress_percentage', 0)}%")
                
                if session.get("error_message"):
                    print(f"📋 Error: {session.get('error_message')}")
            else:
                print(f"❌ Failed to get final status: {response.status_code}")
        except Exception as e:
            print(f"❌ Error getting final status: {e}")
        
        # Step 4: Test export functionality
        print(f"\n4️⃣ Testing export functionality...")
        try:
            # Test JSON export
            response = await client.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/export?format=json")
            if response.status_code == 200:
                export_data = response.json()
                print(f"✅ JSON export successful: {export_data.get('total_users', 0)} users")
            else:
                print(f"❌ JSON export failed: {response.status_code} - {response.text}")
            
            # Test Excel export
            response = await client.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/export?format=xlsx")
            if response.status_code == 200:
                print(f"✅ Excel export successful: {len(response.content)} bytes")
            else:
                print(f"❌ Excel export failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing export: {e}")
        
        # Step 5: Test manual start endpoint
        print(f"\n5️⃣ Testing manual start endpoint...")
        try:
            response = await client.post(f"{BASE_URL}/api/scraping/sessions/{session_id}/start")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Manual start successful: {result.get('message')}")
            else:
                print(f"❌ Manual start failed: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Error testing manual start: {e}")
        
        print("\n🏁 Test completed!")

async def test_zendriver_service():
    """Test zendriver service directly"""
    async with httpx.AsyncClient() as client:
        print("\n🔧 Testing Zendriver Service...")
        
        try:
            response = await client.get(f"{BASE_URL}/api/scraping/test-zendriver-service")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Zendriver service test: {result}")
            else:
                print(f"❌ Zendriver service test failed: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Error testing zendriver service: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting Scraping System Tests")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test zendriver service first
    await test_zendriver_service()
    
    # Test complete scraping flow
    await test_scraping_flow()
    
    print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
