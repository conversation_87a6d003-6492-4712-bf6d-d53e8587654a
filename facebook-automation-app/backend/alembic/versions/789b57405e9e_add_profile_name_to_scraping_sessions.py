"""Add profile_name to scraping_sessions

Revision ID: 789b57405e9e
Revises: df39be2d1d30
Create Date: 2025-07-07 14:08:50.093091

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '789b57405e9e'
down_revision: Union[str, Sequence[str], None] = 'df39be2d1d30'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('idx_campaign_status_created'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_created_at'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_id'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_name'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_status'), table_name='campaigns')
    op.drop_table('campaigns')
    op.drop_index(op.f('ix_messages_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_status'), table_name='messages')
    op.drop_index(op.f('ix_messages_target_uid'), table_name='messages')
    op.drop_table('messages')
    op.drop_index(op.f('ix_message_analytics_date'), table_name='message_analytics')
    op.drop_index(op.f('ix_message_analytics_id'), table_name='message_analytics')
    op.drop_table('message_analytics')
    op.drop_index(op.f('ix_message_templates_id'), table_name='message_templates')
    op.drop_index(op.f('ix_message_templates_name'), table_name='message_templates')
    op.drop_table('message_templates')
    op.drop_index(op.f('ix_message_queue_id'), table_name='message_queue')
    op.drop_index(op.f('ix_message_queue_status'), table_name='message_queue')
    op.drop_table('message_queue')
    op.drop_table('analytics')
    op.drop_index(op.f('idx_message_log_campaign_status'), table_name='message_logs')
    op.drop_index(op.f('idx_message_log_profile_sent'), table_name='message_logs')
    op.drop_index(op.f('idx_message_log_uid_status'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_campaign_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_recipient_uid'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_sender_profile_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_sent_at'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_status'), table_name='message_logs')
    op.drop_table('message_logs')
    op.drop_index(op.f('ix_message_campaigns_id'), table_name='message_campaigns')
    op.drop_index(op.f('ix_message_campaigns_name'), table_name='message_campaigns')
    op.drop_table('message_campaigns')
    op.add_column('scraping_sessions', sa.Column('profile_name', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_scraping_sessions_profile_name'), 'scraping_sessions', ['profile_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_scraping_sessions_profile_name'), table_name='scraping_sessions')
    op.drop_column('scraping_sessions', 'profile_name')
    op.create_table('message_campaigns',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('template_id', sa.INTEGER(), nullable=True),
    sa.Column('profile_id', sa.INTEGER(), nullable=True),
    sa.Column('scraping_session_id', sa.INTEGER(), nullable=True),
    sa.Column('target_user_count', sa.INTEGER(), nullable=True),
    sa.Column('target_filters', sqlite.JSON(), nullable=True),
    sa.Column('scheduled_at', sa.DATETIME(), nullable=True),
    sa.Column('started_at', sa.DATETIME(), nullable=True),
    sa.Column('completed_at', sa.DATETIME(), nullable=True),
    sa.Column('messages_per_minute', sa.INTEGER(), nullable=True),
    sa.Column('delay_between_messages', sa.FLOAT(), nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), nullable=True),
    sa.Column('progress_percentage', sa.FLOAT(), nullable=True),
    sa.Column('current_step', sa.VARCHAR(length=255), nullable=True),
    sa.Column('error_message', sa.TEXT(), nullable=True),
    sa.Column('total_messages', sa.INTEGER(), nullable=True),
    sa.Column('sent_messages', sa.INTEGER(), nullable=True),
    sa.Column('delivered_messages', sa.INTEGER(), nullable=True),
    sa.Column('failed_messages', sa.INTEGER(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['profile_id'], ['profiles.id'], ),
    sa.ForeignKeyConstraint(['scraping_session_id'], ['scraping_sessions.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['message_templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_campaigns_name'), 'message_campaigns', ['name'], unique=False)
    op.create_index(op.f('ix_message_campaigns_id'), 'message_campaigns', ['id'], unique=False)
    op.create_table('message_logs',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('campaign_id', sa.INTEGER(), nullable=False),
    sa.Column('sender_profile_id', sa.INTEGER(), nullable=False),
    sa.Column('recipient_uid', sa.VARCHAR(length=50), nullable=False),
    sa.Column('recipient_name', sa.VARCHAR(length=255), nullable=True),
    sa.Column('message_text', sa.TEXT(), nullable=False),
    sa.Column('image_path', sa.VARCHAR(length=500), nullable=True),
    sa.Column('status', sa.VARCHAR(length=20), nullable=False),
    sa.Column('scheduled_at', sa.DATETIME(), nullable=True),
    sa.Column('sent_at', sa.DATETIME(), nullable=True),
    sa.Column('error_message', sa.TEXT(), nullable=True),
    sa.Column('retry_count', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
    sa.ForeignKeyConstraint(['sender_profile_id'], ['profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_logs_status'), 'message_logs', ['status'], unique=False)
    op.create_index(op.f('ix_message_logs_sent_at'), 'message_logs', ['sent_at'], unique=False)
    op.create_index(op.f('ix_message_logs_sender_profile_id'), 'message_logs', ['sender_profile_id'], unique=False)
    op.create_index(op.f('ix_message_logs_recipient_uid'), 'message_logs', ['recipient_uid'], unique=False)
    op.create_index(op.f('ix_message_logs_id'), 'message_logs', ['id'], unique=False)
    op.create_index(op.f('ix_message_logs_campaign_id'), 'message_logs', ['campaign_id'], unique=False)
    op.create_index(op.f('idx_message_log_uid_status'), 'message_logs', ['recipient_uid', 'status'], unique=False)
    op.create_index(op.f('idx_message_log_profile_sent'), 'message_logs', ['sender_profile_id', 'sent_at'], unique=False)
    op.create_index(op.f('idx_message_log_campaign_status'), 'message_logs', ['campaign_id', 'status'], unique=False)
    op.create_table('analytics',
    sa.Column('id', sa.VARCHAR(), nullable=False),
    sa.Column('metric_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('metric_value', sa.FLOAT(), nullable=False),
    sa.Column('metric_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('profile_id', sa.VARCHAR(), nullable=True),
    sa.Column('campaign_id', sa.VARCHAR(), nullable=True),
    sa.Column('date', sa.DATETIME(), nullable=True),
    sa.Column('extra_metadata', sqlite.JSON(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
    sa.ForeignKeyConstraint(['profile_id'], ['profiles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('message_queue',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('campaign_id', sa.INTEGER(), nullable=False),
    sa.Column('message_id', sa.INTEGER(), nullable=False),
    sa.Column('status', sa.VARCHAR(length=50), nullable=True),
    sa.Column('priority', sa.INTEGER(), nullable=True),
    sa.Column('assigned_worker', sa.VARCHAR(length=255), nullable=True),
    sa.Column('processing_started_at', sa.DATETIME(), nullable=True),
    sa.Column('processing_completed_at', sa.DATETIME(), nullable=True),
    sa.Column('retry_count', sa.INTEGER(), nullable=True),
    sa.Column('next_retry_at', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['message_campaigns.id'], ),
    sa.ForeignKeyConstraint(['message_id'], ['messages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_queue_status'), 'message_queue', ['status'], unique=False)
    op.create_index(op.f('ix_message_queue_id'), 'message_queue', ['id'], unique=False)
    op.create_table('message_templates',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('content', sa.TEXT(), nullable=False),
    sa.Column('variables', sqlite.JSON(), nullable=True),
    sa.Column('message_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('created_by', sa.VARCHAR(length=255), nullable=True),
    sa.Column('usage_count', sa.INTEGER(), nullable=True),
    sa.Column('last_used_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_templates_name'), 'message_templates', ['name'], unique=False)
    op.create_index(op.f('ix_message_templates_id'), 'message_templates', ['id'], unique=False)
    op.create_table('message_analytics',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('campaign_id', sa.INTEGER(), nullable=False),
    sa.Column('date', sa.DATETIME(), nullable=False),
    sa.Column('hour', sa.INTEGER(), nullable=True),
    sa.Column('messages_sent', sa.INTEGER(), nullable=True),
    sa.Column('messages_delivered', sa.INTEGER(), nullable=True),
    sa.Column('messages_failed', sa.INTEGER(), nullable=True),
    sa.Column('avg_delivery_time', sa.FLOAT(), nullable=True),
    sa.Column('success_rate', sa.FLOAT(), nullable=True),
    sa.Column('error_types', sqlite.JSON(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['message_campaigns.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_analytics_id'), 'message_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_message_analytics_date'), 'message_analytics', ['date'], unique=False)
    op.create_table('messages',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('campaign_id', sa.INTEGER(), nullable=False),
    sa.Column('template_id', sa.INTEGER(), nullable=True),
    sa.Column('target_uid', sa.VARCHAR(length=255), nullable=False),
    sa.Column('target_name', sa.VARCHAR(length=255), nullable=True),
    sa.Column('target_profile_url', sa.VARCHAR(length=500), nullable=True),
    sa.Column('content', sa.TEXT(), nullable=False),
    sa.Column('message_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('variables_used', sqlite.JSON(), nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), nullable=True),
    sa.Column('scheduled_at', sa.DATETIME(), nullable=True),
    sa.Column('sent_at', sa.DATETIME(), nullable=True),
    sa.Column('delivered_at', sa.DATETIME(), nullable=True),
    sa.Column('failed_at', sa.DATETIME(), nullable=True),
    sa.Column('error_message', sa.TEXT(), nullable=True),
    sa.Column('retry_count', sa.INTEGER(), nullable=True),
    sa.Column('max_retries', sa.INTEGER(), nullable=True),
    sa.Column('facebook_message_id', sa.VARCHAR(length=255), nullable=True),
    sa.Column('conversation_id', sa.VARCHAR(length=255), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['message_campaigns.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['message_templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_target_uid'), 'messages', ['target_uid'], unique=False)
    op.create_index(op.f('ix_messages_status'), 'messages', ['status'], unique=False)
    op.create_index(op.f('ix_messages_id'), 'messages', ['id'], unique=False)
    op.create_table('campaigns',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('message_text', sa.TEXT(), nullable=False),
    sa.Column('image_path', sa.VARCHAR(length=500), nullable=True),
    sa.Column('settings', sa.TEXT(), nullable=False),
    sa.Column('max_concurrent_threads', sa.INTEGER(), nullable=False),
    sa.Column('message_delay_min', sa.FLOAT(), nullable=False),
    sa.Column('message_delay_max', sa.FLOAT(), nullable=False),
    sa.Column('target_uids', sa.TEXT(), nullable=False),
    sa.Column('exclude_sent_uids', sa.BOOLEAN(), nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), nullable=False),
    sa.Column('progress_percentage', sa.FLOAT(), nullable=False),
    sa.Column('total_targets', sa.INTEGER(), nullable=False),
    sa.Column('messages_sent', sa.INTEGER(), nullable=False),
    sa.Column('messages_failed', sa.INTEGER(), nullable=False),
    sa.Column('messages_skipped', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('started_at', sa.DATETIME(), nullable=True),
    sa.Column('completed_at', sa.DATETIME(), nullable=True),
    sa.Column('error_message', sa.TEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_campaigns_status'), 'campaigns', ['status'], unique=False)
    op.create_index(op.f('ix_campaigns_name'), 'campaigns', ['name'], unique=False)
    op.create_index(op.f('ix_campaigns_id'), 'campaigns', ['id'], unique=False)
    op.create_index(op.f('ix_campaigns_created_at'), 'campaigns', ['created_at'], unique=False)
    op.create_index(op.f('idx_campaign_status_created'), 'campaigns', ['status', 'created_at'], unique=False)
    # ### end Alembic commands ###
