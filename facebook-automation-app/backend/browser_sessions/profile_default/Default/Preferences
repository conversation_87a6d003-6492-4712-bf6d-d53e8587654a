{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137, "metadata_upload_events": {"2C5": 1}, "upload_encoding_seed": "0A95355C3FE7BAC69A720B114CAB4BD4", "upload_events_last_reset_timestamp": "*****************"}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1420, "left": 420, "maximized": false, "right": 1620, "top": 49, "work_area_bottom": 1440, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "e5a2b25e-6c1b-4215-a9ab-b8ee9eecd9ae", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.7151.122"}, "gaia_cookie": {"changed_time": **********.803486, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.macosx", "push_messaging_unsubscribed_entries_list": []}, "google": {"services": {"signin_scoped_device_id": "4d5de79b-1f5a-408e-9b72-e011b9273137"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "F10E2D10AFDE8DDE4C10C710ACB6C6BD", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "EEZ7Crs9xZ4tTNTLODnwB2EjyZx31EFhVpNPm+FHnUbjQ2/Eh/AM4P5uhiSPxAIh4/OOm2diyZo7je5kDiLabQ=="}, "ntp": {"num_personal_suggestions": 7}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.facebook.com:443,*": {"last_modified": "*****************", "setting": {"https://www.facebook.com/": {"next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}, "https://www.facebook.com/?ref=homescreenpwa": {"couldShowBannerEvents": 1.3396337396590376e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.facebook.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]facebook.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://www.facebook.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 9}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {"https://www.facebook.com:443,*": {"last_modified": "13396341737896476", "setting": 2}}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13396355682416206", "setting": {"lastEngagementTime": 1.3396355682416194e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 5.928}}, "https://www.facebook.com:443,*": {"last_modified": "13396373977117069", "setting": {"lastEngagementTime": 1.339637397711706e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 14.3890587648}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"notifications": [{"action": 1, "prompt_disposition": 12, "time": "13396341737896797"}]}, "pref_version": 1}, "created_by_version": "137.0.7151.122", "creation_time": "13396337258155032", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13396373977117059", "last_time_obsolete_http_credentials_removed": 1751863718.1745, "last_time_password_store_metrics_reported": 1751863688.172842, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13396596458790637", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13396337258", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQhJ6Fn9385RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEKqehZ/d/OUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396233599000000", "uma_in_sql_start_time": "13396337258179168"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396341616566355", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396341624884180", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396341627541134", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396341692688019", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396341746515657", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396341767823914", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396341770933331", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396351675459571", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396351717715176", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396353847316252", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396353896713060", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396353911266334", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396355595876066", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396355651174502", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396355671007274", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396355682276392", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396355685371219", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396373956337113", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396373982710965", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"PasswordSignInPromoShownCount": 1, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"]}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.facebook.com/?ref=homescreenpwa": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"liu grace\",\"giá lúa gạo\",\"leon masters\",\"lũ lụt texas\",\"apple iphone 17 pro max\",\"gi<PERSON> thép hôm nay\",\"lịch âm hôm nay\",\"diogo jota andré silva\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"Ci0Ikk4SKAokTuG7mWkgZHVuZyB0w6xtIGtp4bq/bSB0aOG7i25oIGjDoG5oKAo\\u003d\",\"google:suggestdetail\":[{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"3503479748512120537\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}