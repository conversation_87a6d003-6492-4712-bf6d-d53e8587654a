#!/usr/bin/env python3
"""
Antidetect browser script for checking profile with saved Facebook cookies.
Profile: Real Facebook Profile (ID: cbba1487-c2c5-4e77-8269-1fdee31b157e)
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from automation.browser_manager import BrowserManager
    from automation.facebook_session import FacebookSession
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running from the correct directory")
    sys.exit(1)

async def main():
    """Launch antidetect browser for profile checking."""
    try:
        print("Starting antidetect browser for profile checking...")
        print("Profile: Real Facebook Profile (ID: cbba1487-c2c5-4e77-8269-1fdee31b157e)")

        # Browser configuration with antidetect settings
        config = {
        "user_data_dir": "/var/folders/4x/pqrmqft56vbcg_qz5m_t8ms00000gn/T/browser_profiles/profile_cbba1487-c2c5-4e77-8269-1fdee31b157e",
        "headless": false,
        "sandbox": false,
        "browser_connection_timeout": 10.0,
        "browser_connection_max_tries": 15,
        "browser_executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "browser_args": [
                "--window-size=1920,1080",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-except",
                "--disable-plugins-discovery",
                "--no-first-run",
                "--no-service-autorun",
                "--password-store=basic",
                "--disable-dev-shm-usage",
                "--remote-allow-origins=*",
                "--disable-webrtc",
                "--disable-background-networking"
        ]
}

        # Initialize browser manager
        browser_manager = BrowserManager()

        # Launch browser with saved cookies
        browser = await browser_manager.launch_browser(config)

        if browser:
            print("✅ Browser launched successfully with saved cookies!")
            print("🔍 Checking Facebook login status...")

            # Navigate to Facebook to check login status
            page = browser.main_tab
            await page.get("https://www.facebook.com")

            # Wait a bit for page to load
            await asyncio.sleep(3)

            # Check if logged in by looking for profile elements
            try:
                # This is a simple check - in production you'd want more robust detection
                current_url = await page.evaluate("window.location.href")
                page_title = await page.evaluate("document.title")

                print(f"Current URL: {current_url}")
                print(f"Page title: {page_title}")

                if "login" in current_url.lower() or "login" in page_title.lower():
                    print("⚠️  Not logged in - redirected to login page")
                else:
                    print("✅ Successfully logged in to Facebook!")
                    print("Facebook profile is accessible with saved cookies")

            except Exception as e:
                print(f"Error checking login status: {e}")

            print("\n🌐 Browser is ready for manual inspection")
            print("Close the browser window when done")

            # Keep the browser open until manually closed
            try:
                while True:
                    await asyncio.sleep(1)
                    # Check if browser is still alive
                    try:
                        await page.evaluate("1")
                    except:
                        print("Browser closed by user")
                        break
            except KeyboardInterrupt:
                print("\nShutting down...")
            finally:
                try:
                    await browser.stop()
                except:
                    pass
        else:
            print("❌ Failed to launch browser")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
