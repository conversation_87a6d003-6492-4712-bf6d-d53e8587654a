"""
Scraped data models optimized for bulk operations and queries.
"""
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Integer, String, Text, Index, UniqueConstraint, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.database import Base


class ScrapingType(str, Enum):
    """Types of scraping operations."""
    COMMENTS = "comments"
    LIKES = "likes"
    SHARES = "shares"
    REACTIONS = "reactions"


class Gender(str, Enum):
    """User gender options."""
    MALE = "male"
    FEMALE = "female"
    UNKNOWN = "unknown"


class ScrapingSession(Base):
    """
    Scraping session to track bulk operations.
    """
    __tablename__ = "scraping_sessions"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Session info
    name: Mapped[str] = mapped_column(String(255), index=True)
    post_url: Mapped[str] = mapped_column(String(1000), index=True)
    profile_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    scraping_types: Mapped[str] = mapped_column(String(100))  # JSON array of types
    
    # Statistics
    total_found: Mapped[int] = mapped_column(Integer, default=0)
    unique_users: Mapped[int] = mapped_column(Integer, default=0)
    
    # Status tracking
    status: Mapped[str] = mapped_column(
        String(20), 
        default="running",
        index=True
    )  # running, completed, failed, cancelled
    
    # Progress tracking
    progress_percentage: Mapped[float] = mapped_column(Integer, default=0)
    current_step: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Timestamps
    started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, index=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Relationships
    scraped_users: Mapped[list["ScrapedUser"]] = relationship(
        "ScrapedUser",
        back_populates="session",
        cascade="all, delete-orphan"
    )
    # message_campaigns = relationship("MessageCampaign", back_populates="scraping_session")  # Commented out until MessageCampaign is implemented
    
    def __repr__(self):
        return f"<ScrapingSession(id={self.id}, name='{self.name}', status='{self.status}')>"


class ScrapedUser(Base):
    """
    Scraped user data with optimized indexing for bulk operations.
    """
    __tablename__ = "scraped_users"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Foreign key to session
    session_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("scraping_sessions.id"),
        nullable=False,
        index=True  # Index for session-based queries
    )
    
    # User identification
    uid: Mapped[str] = mapped_column(
        String(50), 
        nullable=False,
        index=True  # Critical index for UID lookups
    )
    
    # User information
    name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, index=True)
    gender: Mapped[Gender] = mapped_column(
        String(10), 
        default=Gender.UNKNOWN,
        index=True  # For filtering by gender
    )
    profile_url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)
    
    # Interaction data
    interaction_type: Mapped[ScrapingType] = mapped_column(
        String(20),
        index=True  # For filtering by interaction type
    )
    comment_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Messaging tracking
    message_sent: Mapped[bool] = mapped_column(
        Boolean, 
        default=False,
        index=True  # For finding unsent messages
    )
    message_sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    message_sent_by_profile_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Timestamps
    scraped_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        index=True
    )
    
    # Relationship
    session: Mapped["ScrapingSession"] = relationship(
        "ScrapingSession", 
        back_populates="scraped_users"
    )
    
    def __repr__(self):
        return f"<ScrapedUser(id={self.id}, uid='{self.uid}', name='{self.name}')>"


# Composite indexes for performance optimization
Index("idx_scraped_user_uid_session", ScrapedUser.uid, ScrapedUser.session_id)
Index("idx_scraped_user_message_status", ScrapedUser.message_sent, ScrapedUser.scraped_at)
Index("idx_scraped_user_interaction", ScrapedUser.interaction_type, ScrapedUser.scraped_at)
Index("idx_session_status_started", ScrapingSession.status, ScrapingSession.started_at)

# Unique constraint to prevent duplicate UIDs in same session
UniqueConstraint(
    ScrapedUser.uid, 
    ScrapedUser.session_id, 
    name="uq_scraped_user_uid_session"
)
