"""
Profile and Proxy models optimized for performance.
"""
import json
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateT<PERSON>, Integer, String, Text, Index, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.database import Base


class ProxyType(str, Enum):
    """Proxy types supported by the application for antidetect browser."""
    NO_PROXY = "no_proxy"  # Local network connection
    HTTP = "http"          # HTTP proxy
    HTTPS = "https"        # HTTPS proxy
    SOCKS5 = "socks5"      # SOCKS5 proxy
    SSH = "ssh"            # SSH tunnel proxy


class ProfileStatus(str, Enum):
    """Profile status for tracking."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ERROR = "error"


class Profile(Base):
    """
    Browser profile model compatible with existing database schema.
    """
    __tablename__ = "profiles"

    # Primary key - VARCHAR to match existing schema
    id: Mapped[str] = mapped_column(String, primary_key=True, index=True)

    # Basic info - match existing schema
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[str] = mapped_column(String(50), nullable=True)

    # Browser configuration
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    screen_resolution: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    timezone: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)

    # Proxy configuration - inline in profile table for existing schema
    proxy_host: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    proxy_port: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    proxy_username: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    proxy_password: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    proxy_type: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)

    # Facebook configuration
    facebook_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    facebook_password: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    facebook_user_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    facebook_username: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    facebook_logged_in: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Profile directory path
    profile_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Timestamps - match existing schema
    last_used: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Add properties for compatibility
    @property
    def last_used_at(self):
        return self.last_used

    @property
    def login_count(self):
        return 0  # Default value for compatibility

    @property
    def message_sent_count(self):
        return 0  # Default value for compatibility

    @property
    def browser_config(self):
        return None  # Default value for compatibility

    def get_browser_config(self) -> dict:
        """Get browser configuration as dictionary."""
        return {}  # Return empty dict for compatibility

    def set_browser_config(self, config: dict):
        """Set browser configuration from dictionary."""
        pass  # No-op for compatibility

    def get_proxy_config(self) -> dict:
        """Get proxy configuration as dictionary."""
        if self.proxy_type and self.proxy_type != "no_proxy":
            config = {
                "proxy_type": self.proxy_type,
                "host": self.proxy_host,
                "port": self.proxy_port,
            }
            if self.proxy_username and self.proxy_password:
                config.update({
                    "username": self.proxy_username,
                    "password": self.proxy_password,
                })
            return config
        return {}

    @property
    def proxy_config(self) -> dict:
        """Property to get proxy configuration for Pydantic serialization."""
        return self.get_proxy_config()

    def __repr__(self):
        return f"<Profile(id={self.id}, name='{self.name}', status='{self.status}')>"


# ProxyConfig model commented out for compatibility with existing schema
# Will be re-enabled after proper migration
class ProxyConfig:
    """
    Temporary proxy configuration class for compatibility.
    """
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def get_proxy_url(self) -> Optional[str]:
        """Generate proxy URL for antidetect browser configuration."""
        return None

    def get_browser_proxy_config(self) -> dict:
        """Get proxy configuration formatted for antidetect browser."""
        return {"mode": "direct"}

    def validate_proxy_config(self) -> tuple[bool, str]:
        """Validate proxy configuration for antidetect browser compatibility."""
        return True, "No proxy configuration"

