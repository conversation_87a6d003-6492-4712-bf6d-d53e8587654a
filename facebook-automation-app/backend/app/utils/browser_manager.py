"""
Enhanced browser manager with session management and cookie persistence.
"""
import asyncio
import json
import secrets
import shutil
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, List, Any
from contextlib import asynccontextmanager

import sys
sys.path.append(str(Path(__file__).parent.parent.parent / "zendriver_local"))

# import zendriver as zd  # Temporarily disabled for compatibility
from loguru import logger

from ..core.config import settings
from ..models import Profile, ProxyConfig, ProxyType


class BrowserPool:
    """
    Browser pool for efficient resource management.
    Optimized for desktop app with limited concurrent browsers.
    """
    
    def __init__(self, max_browsers: int = None):
        self.max_browsers = max_browsers or settings.max_browser_instances
        self._browsers: Dict[int, zd.Browser] = {}  # profile_id -> browser
        self._browser_configs: Dict[int, zd.Config] = {}  # profile_id -> config
        self._lock = asyncio.Lock()
        
    async def get_browser(self, profile: Profile) -> zd.Browser:
        """Get or create browser for profile."""
        async with self._lock:
            if profile.id in self._browsers:
                # Check if browser is still alive
                try:
                    browser = self._browsers[profile.id]
                    # Simple health check
                    if browser.connection and browser.connection.websocket:
                        return browser
                except Exception:
                    # Browser is dead, remove it
                    await self._cleanup_browser(profile.id)
            
            # Create new browser
            return await self._create_browser(profile)
    
    async def _create_browser(self, profile: Profile) -> zd.Browser:
        """Create new browser instance for profile."""
        # Check pool limit
        if len(self._browsers) >= self.max_browsers:
            # Remove oldest browser
            oldest_profile_id = next(iter(self._browsers))
            await self._cleanup_browser(oldest_profile_id)
        
        # Create profile directory
        profile_dir = settings.profiles_dir / f"profile_{profile.id}"
        profile_dir.mkdir(exist_ok=True)
        
        # Create browser config
        config = await self._create_browser_config(profile, profile_dir)
        
        try:
            # Start browser
            browser = await zd.start(config=config)
            
            # Store in pool
            self._browsers[profile.id] = browser
            self._browser_configs[profile.id] = config
            
            logger.info(f"Created browser for profile {profile.id}")
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create browser for profile {profile.id}: {e}")
            raise
    
    async def _create_browser_config(self, profile: Profile, profile_dir: Path) -> zd.Config:
        """Create optimized browser configuration."""
        # Base config for performance
        config = zd.Config(
            user_data_dir=str(profile_dir),
            headless=False,  # For debugging, can be True in production
            sandbox=False,  # Disable sandbox for macOS compatibility
            browser_executable_path=self._get_chrome_executable_path(),
            browser_args=[
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-background-networking",
                "--disable-sync",
                "--disable-default-apps",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-preconnect-resource-hints",
                "--disable-prefetch-resource-hints",
                # Memory optimizations
                "--memory-pressure-off",
                "--max_old_space_size=4096",
                # Performance optimizations
                "--aggressive-cache-discard",
                "--enable-fast-unload",
            ]
        )
        
        # Add proxy configuration if available
        if profile.proxy_config:
            proxy_url = profile.proxy_config.get_proxy_url()
            if proxy_url:
                config.browser_args.extend([
                    f"--proxy-server={proxy_url}",
                    "--proxy-bypass-list=<-loopback>"
                ])
        
        # Add custom user agent if specified
        if profile.user_agent:
            config.browser_args.append(f"--user-agent={profile.user_agent}")
        
        return config

    def _get_chrome_executable_path(self) -> str:
        """Get Chrome executable path for different platforms."""
        import platform
        import os

        system = platform.system().lower()

        # Common Chrome paths for different platforms
        chrome_paths = []

        if system == "darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        elif system == "linux":
            chrome_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium",
            ]
        elif system == "windows":
            chrome_paths = [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe".format(os.getenv("USERNAME", "")),
            ]

        # Find first existing path
        for path in chrome_paths:
            if os.path.exists(path):
                return path

        # If no path found, return None to let zendriver auto-detect
        return None
    
    async def _cleanup_browser(self, profile_id: int):
        """Clean up browser instance."""
        if profile_id in self._browsers:
            try:
                browser = self._browsers[profile_id]
                await browser.stop()
            except Exception as e:
                logger.warning(f"Error stopping browser for profile {profile_id}: {e}")
            finally:
                self._browsers.pop(profile_id, None)
                self._browser_configs.pop(profile_id, None)
    
    async def close_browser(self, profile_id: int):
        """Close specific browser."""
        async with self._lock:
            await self._cleanup_browser(profile_id)
    
    async def close_all(self):
        """Close all browsers in pool."""
        async with self._lock:
            for profile_id in list(self._browsers.keys()):
                await self._cleanup_browser(profile_id)
    
    def get_active_browsers(self) -> List[int]:
        """Get list of active browser profile IDs."""
        return list(self._browsers.keys())


class BrowserManager:
    """
    High-level browser manager with session management.
    """
    
    def __init__(self):
        self.pool = BrowserPool()
        self._sessions: Dict[int, dict] = {}  # profile_id -> session_info
    
    @asynccontextmanager
    async def get_browser_session(self, profile: Profile):
        """Get browser session with automatic cleanup."""
        browser = None
        try:
            browser = await self.pool.get_browser(profile)
            
            # Update profile usage
            profile.last_used_at = profile.updated_at
            profile.login_count += 1
            
            # Track session
            self._sessions[profile.id] = {
                "started_at": profile.last_used_at,
                "browser": browser
            }
            
            yield browser
            
        except Exception as e:
            logger.error(f"Browser session error for profile {profile.id}: {e}")
            raise
        finally:
            # Clean up session tracking
            self._sessions.pop(profile.id, None)
    
    async def close_profile_browser(self, profile_id: int):
        """Close browser for specific profile."""
        await self.pool.close_browser(profile_id)
    
    async def close_all_browsers(self):
        """Close all browsers."""
        await self.pool.close_all()
    
    def get_browser_status(self) -> dict:
        """Get status of all browsers."""
        return {
            "active_browsers": len(self.pool._browsers),
            "max_browsers": self.pool.max_browsers,
            "active_profiles": self.pool.get_active_browsers(),
            "sessions": list(self._sessions.keys())
        }


# Global browser manager instance
browser_manager = BrowserManager()
