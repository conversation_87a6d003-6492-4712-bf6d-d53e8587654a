"""
Facebook service for handling login and automation with antidetect browser.
"""
import asyncio
import json
import os
import time
from typing import Dict, Any, Optional, Tuple
import logging

from ..models.profile import Profile
from .browser_service import BrowserService
from .zendriver_service import zendriver_service

logger = logging.getLogger(__name__)


class FacebookService:
    """Service for Facebook login and automation using antidetect browser."""
    
    def __init__(self):
        self.browser_service = BrowserService()
        self.login_timeout = 300  # 5 minutes for manual login
        
    async def initiate_login(self, profile: Profile) -> Dict[str, Any]:
        """Initiate Facebook login session with antidetect browser using zendriver."""
        try:
            logger.info(f"Initiating Facebook login for profile {profile.id}")

            # Use zendriver service to launch browser with antidetect configuration
            result = await zendriver_service.launch_browser_for_facebook_login(profile)

            if result["status"] == "browser_launched":
                # Save session data for tracking
                browser_config = self.browser_service.create_browser_config(profile)
                session_file = os.path.join(
                    browser_config["profile_path"],
                    "facebook_login_session.json"
                )

                # Ensure directory exists
                os.makedirs(os.path.dirname(session_file), exist_ok=True)

                session_data = {
                    "profile_id": profile.id,
                    "profile_name": profile.name,
                    "session_id": result.get("session_id"),
                    "login_url": "https://www.facebook.com/login",
                    "session_started": time.time(),
                    "status": "browser_launched"
                }

                with open(session_file, 'w', encoding='utf-8') as f:
                    json.dump(session_data, f, indent=2, default=str)

                logger.info(f"Facebook login browser launched for profile {profile.id}")

                return {
                    "status": "login_initiated",
                    "message": "Antidetect browser launched successfully. Complete Facebook login manually.",
                    "session_id": result.get("session_id"),
                    "instructions": result.get("instructions", self._get_login_instructions())
                }
            else:
                return result

        except asyncio.TimeoutError:
            logger.error(f"Timeout initiating Facebook login for profile {profile.id}")
            return {
                "status": "error",
                "message": "Facebook login initiation timed out. Please try again."
            }
        except Exception as e:
            logger.error(f"Error initiating Facebook login for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to initiate login: {str(e)}"
            }
    
    def _get_login_instructions(self) -> list:
        """Get step-by-step login instructions."""
        return [
            "1. Antidetect browser will open with your profile configuration",
            "2. Navigate to https://www.facebook.com/login",
            "3. Enter your Facebook email/phone and password manually",
            "4. Complete any 2FA verification if prompted",
            "5. Solve any CAPTCHA or security challenges",
            "6. Once logged in and on Facebook homepage, click 'Complete Login' button",
            "7. System will automatically detect login success and update profile status"
        ]
    
    async def check_login_status(self, profile: Profile) -> Dict[str, Any]:
        """Check if Facebook login is complete by examining browser session."""
        try:
            # Check zendriver session status
            zendriver_status = zendriver_service.get_session_status(profile.id)

            # Also check local session file
            browser_config = self.browser_service.create_browser_config(profile)
            session_file = os.path.join(
                browser_config["profile_path"],
                "facebook_login_session.json"
            )

            if not os.path.exists(session_file):
                return {
                    "status": "no_session",
                    "message": "No active login session found"
                }

            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            # Check session age
            session_age = time.time() - session_data.get("session_started", 0)
            if session_age > self.login_timeout:
                return {
                    "status": "expired",
                    "message": "Login session expired. Please start a new login session."
                }

            # Combine zendriver status with session data
            if zendriver_status["status"] == "active":
                return {
                    "status": "in_progress",
                    "message": "Browser session is active. Complete login manually in browser.",
                    "session_age": session_age,
                    "timeout_remaining": max(0, self.login_timeout - session_age),
                    "browser_status": "active"
                }
            elif zendriver_status["status"] == "expired":
                return {
                    "status": "browser_closed",
                    "message": "Browser session has ended. Please restart login process.",
                    "session_age": session_age
                }
            else:
                return zendriver_status

        except Exception as e:
            logger.error(f"Error checking login status for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to check login status: {str(e)}"
            }
    
    async def complete_login(self, profile: Profile, facebook_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Complete Facebook login and extract user information."""
        try:
            browser_config = self.browser_service.create_browser_config(profile)
            session_file = os.path.join(
                browser_config["profile_path"],
                "facebook_login_session.json"
            )

            # Update session data
            if os.path.exists(session_file):
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)

                session_data.update({
                    "status": "completed",
                    "completed_at": time.time(),
                    "facebook_data": facebook_data or {}
                })

                with open(session_file, 'w', encoding='utf-8') as f:
                    json.dump(session_data, f, indent=2, default=str)

            # Terminate the browser session
            await zendriver_service.terminate_session(profile.id)

            # For now, we'll use provided data or mock data
            extracted_data = facebook_data or self._extract_mock_facebook_data()

            logger.info(f"Facebook login completed for profile {profile.id}")

            return {
                "status": "login_complete",
                "message": "Facebook login completed successfully",
                "facebook_data": extracted_data,
                "profile_ready": True
            }

        except Exception as e:
            logger.error(f"Error completing Facebook login for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to complete login: {str(e)}"
            }

    async def terminate_login_session(self, profile: Profile) -> Dict[str, Any]:
        """Terminate active Facebook login session."""
        try:
            # Terminate zendriver session
            result = await zendriver_service.terminate_session(profile.id)

            # Clean up session file
            browser_config = self.browser_service.create_browser_config(profile)
            session_file = os.path.join(
                browser_config["profile_path"],
                "facebook_login_session.json"
            )

            if os.path.exists(session_file):
                os.remove(session_file)

            logger.info(f"Facebook login session terminated for profile {profile.id}")

            return {
                "status": "session_terminated",
                "message": "Facebook login session terminated successfully",
                "zendriver_result": result
            }

        except Exception as e:
            logger.error(f"Error terminating login session for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to terminate session: {str(e)}"
            }
    
    def _extract_mock_facebook_data(self) -> Dict[str, Any]:
        """Extract mock Facebook data for testing purposes."""
        return {
            "email": "<EMAIL>",
            "username": "facebook_user",
            "user_id": "123456789",
            "name": "Facebook User",
            "profile_url": "https://www.facebook.com/facebook_user"
        }
    
    async def logout(self, profile: Profile) -> Dict[str, Any]:
        """Logout from Facebook and clear session data."""
        try:
            browser_config = self.browser_service.create_browser_config(profile)
            session_file = os.path.join(
                browser_config["profile_path"], 
                "facebook_login_session.json"
            )
            
            # Remove session file
            if os.path.exists(session_file):
                os.remove(session_file)
            
            # TODO: In a real implementation, you would:
            # 1. Use zendriver to navigate to Facebook logout
            # 2. Clear all cookies and session data
            # 3. Verify logout success
            
            logger.info(f"Facebook logout completed for profile {profile.id}")
            
            return {
                "status": "logout_complete",
                "message": "Successfully logged out from Facebook"
            }
            
        except Exception as e:
            logger.error(f"Error logging out from Facebook for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to logout: {str(e)}"
            }
    
    def get_browser_launch_command(self, profile: Profile) -> Dict[str, Any]:
        """Get command to launch antidetect browser for manual login."""
        try:
            zendriver_config = self.browser_service.get_zendriver_config(profile)
            
            # Create browser launch command
            # This would be used to launch the actual browser
            command_parts = [
                "python", "-c",
                f"""
import asyncio
from zendriver import start

async def launch_browser():
    browser = await start({json.dumps(zendriver_config)})
    page = await browser.get_page()
    await page.goto('https://www.facebook.com/login')
    print('Browser launched. Complete login manually.')
    # Keep browser open for manual login
    await asyncio.sleep(3600)  # 1 hour timeout

asyncio.run(launch_browser())
"""
            ]
            
            return {
                "status": "command_ready",
                "command": command_parts,
                "config": zendriver_config,
                "instructions": "Run this command to launch antidetect browser for manual Facebook login"
            }
            
        except Exception as e:
            logger.error(f"Error creating browser launch command for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to create launch command: {str(e)}"
            }

    async def check_profile_with_cookies(self, profile: Profile) -> Dict[str, Any]:
        """Check profile by opening antidetect browser with saved Facebook cookies."""
        try:
            logger.info(f"Checking profile {profile.id} with saved Facebook cookies")

            # Check if profile has Facebook login info
            if not profile.facebook_email and not profile.facebook_user_id:
                return {
                    "status": "no_cookies",
                    "message": "No Facebook login information found. Please login to Facebook first.",
                    "profile_id": profile.id,
                    "profile_name": profile.name
                }

            # Check if cookies exist in profile directory
            browser_config = self.browser_service.create_browser_config(profile)
            profile_path = browser_config["profile_path"]
            cookies_file = os.path.join(profile_path, "Default", "Cookies")

            if not os.path.exists(cookies_file):
                return {
                    "status": "no_cookies",
                    "message": "No browser cookies found. Please login to Facebook first.",
                    "profile_id": profile.id,
                    "profile_name": profile.name
                }

            # Launch browser with saved cookies using zendriver
            result = await zendriver_service.launch_browser_with_cookies(profile)

            if result["status"] == "browser_launched":
                return {
                    "status": "browser_launched",
                    "message": f"Antidetect browser opened with saved Facebook cookies for profile '{profile.name}'",
                    "profile_id": profile.id,
                    "profile_name": profile.name,
                    "facebook_email": profile.facebook_email,
                    "facebook_user_id": profile.facebook_user_id,
                    "session_id": result.get("session_id")
                }
            else:
                return {
                    "status": "error",
                    "message": f"Failed to launch browser: {result.get('message', 'Unknown error')}",
                    "profile_id": profile.id,
                    "profile_name": profile.name
                }

        except Exception as e:
            logger.error(f"Error checking profile {profile.id} with cookies: {e}")
            return {
                "status": "error",
                "message": f"Failed to check profile: {str(e)}",
                "profile_id": profile.id,
                "profile_name": profile.name
            }
