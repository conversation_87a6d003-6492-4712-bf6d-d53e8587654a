"""
Scraping API endpoints with enhanced Facebook scraping capabilities.
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field, HttpUrl
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs

# Import async database and models
from app.core.database import get_db
from app.models.scraped_data import ScrapingSession, ScrapedUser, ScrapingType, Gender

# Setup logger first
logger = logging.getLogger(__name__)

# Import services with fallback
try:
    from app.services.enhanced_scraper_service import enhanced_facebook_scraper_service
except ImportError:
    enhanced_facebook_scraper_service = None

# Import new Zendriver-based scraper service
try:
    from automation.facebook_scraper_service import FacebookScraperService
    facebook_scraper_service = FacebookScraperService()
    logger.info("FacebookScraperService initialized successfully")
except ImportError as e:
    logger.warning(f"Failed to import FacebookScraperService: {e}")
    facebook_scraper_service = None
except Exception as e:
    logger.error(f"Error initializing FacebookScraperService: {e}")
    facebook_scraper_service = None

router = APIRouter(prefix="/api/scraping", tags=["scraping"])


def serialize_datetime(dt):
    """Convert datetime object to ISO string format"""
    if dt is None:
        return None
    if isinstance(dt, datetime):
        return dt.isoformat()
    return dt


def serialize_scraping_session_response(session: ScrapingSession) -> dict:
    """Convert ScrapingSession model to response dict with proper datetime serialization"""
    return {
        "id": session.id,
        "name": session.name,
        "post_url": session.post_url,
        "scraping_types": session.scraping_types,
        "status": session.status,
        "total_found": session.total_found or 0,
        "unique_users": session.unique_users or 0,
        "progress_percentage": session.progress_percentage or 0.0,
        "current_step": session.current_step,
        "started_at": serialize_datetime(session.started_at),
        "completed_at": serialize_datetime(session.completed_at),
        "error_message": session.error_message,
    }


# Pydantic models
class ScrapingConfigCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    post_url: HttpUrl
    profile_id: str = Field(..., description="Profile ID to use for scraping")
    scraping_types: List[ScrapingType] = [ScrapingType.COMMENTS]
    max_users: Optional[int] = Field(None, ge=1, le=10000)
    include_comments: bool = True
    include_likes: bool = False
    include_shares: bool = False
    include_reactions: bool = False
    delay_between_requests: Optional[int] = Field(default=2, ge=1, le=10)


class ScrapingSessionResponse(BaseModel):
    id: int
    name: str
    post_url: str
    scraping_types: str
    status: str
    total_found: int
    unique_users: int
    progress_percentage: float
    current_step: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    error_message: Optional[str]

    class Config:
        from_attributes = True


class ScrapedUserResponse(BaseModel):
    id: int
    uid: str
    name: str
    profile_url: Optional[str]
    gender: Optional[Gender]
    location: Optional[str]
    comment_content: Optional[str]
    scraped_at: str

    class Config:
        from_attributes = True


class ScrapingStatsResponse(BaseModel):
    total_sessions: int
    active_sessions: int
    completed_sessions: int
    total_users_scraped: int
    unique_users: int


def extract_facebook_uid(profile_url: str) -> Optional[str]:
    """Extract Facebook UID from profile URL."""
    if not profile_url:
        return None

    # Pattern for URLs like: https://www.facebook.com/groups/591054007361950/user/1836579734
    uid_pattern = r'/user/(\d+)'
    match = re.search(uid_pattern, profile_url)
    if match:
        return match.group(1)

    # Pattern for direct profile URLs: https://www.facebook.com/profile.php?id=1836579734
    if 'profile.php?id=' in profile_url:
        parsed = urlparse(profile_url)
        query_params = parse_qs(parsed.query)
        if 'id' in query_params:
            return query_params['id'][0]

    # Pattern for username URLs: https://www.facebook.com/username
    username_pattern = r'facebook\.com/([^/?]+)'
    match = re.search(username_pattern, profile_url)
    if match:
        username = match.group(1)
        # Skip common non-username paths
        if username not in ['groups', 'pages', 'profile.php', 'photo.php']:
            return username

    return None


@router.get("/sessions", response_model=List[ScrapingSessionResponse])
async def get_scraping_sessions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of scraping sessions."""
    try:
        query = select(ScrapingSession)

        if status:
            query = query.where(ScrapingSession.status == status)

        query = query.offset(skip).limit(limit).order_by(ScrapingSession.id.desc())
        result = await db.execute(query)
        sessions = result.scalars().all()

        # Serialize sessions with proper datetime handling
        serialized_sessions = [serialize_scraping_session_response(session) for session in sessions]
        return serialized_sessions
    except Exception as e:
        logger.error(f"Error fetching scraping sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping sessions")


@router.get("/sessions/{session_id}", response_model=ScrapingSessionResponse)
async def get_scraping_session(session_id: int, db: AsyncSession = Depends(get_db)):
    """Get specific scraping session."""
    try:
        result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Serialize session with proper datetime handling
        return serialize_scraping_session_response(session)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching scraping session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping session")


@router.post("/sessions", response_model=ScrapingSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_scraping_session(
    config: ScrapingConfigCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Create new scraping session."""
    try:
        # Create scraping session
        session = ScrapingSession(
            name=config.name,
            post_url=str(config.post_url),
            profile_name=config.profile_id,  # Use profile_id as profile_name
            scraping_types=",".join([t.value for t in config.scraping_types]),
            status="pending",
            total_found=0,
            unique_users=0,
            progress_percentage=0.0
        )

        db.add(session)
        await db.commit()
        await db.refresh(session)

        # DO NOT auto-start scraping - wait for manual start button click
        logger.info(f"Created scraping session: {session.name} (ID: {session.id}) - Status: {session.status}")
        logger.info("Session created successfully. Use 'Start' button to begin scraping with antidetect browser.")

        return serialize_scraping_session_response(session)

    except Exception as e:
        logger.error(f"Error creating scraping session: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create scraping session")


@router.delete("/sessions/{session_id}")
async def delete_scraping_session(session_id: int, db: AsyncSession = Depends(get_db)):
    """Delete scraping session and associated data."""
    try:
        # Get session
        result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Delete associated scraped users
        await db.execute(select(ScrapedUser).where(ScrapedUser.session_id == session_id))

        # Delete session
        await db.delete(session)
        await db.commit()

        logger.info(f"Deleted scraping session: {session_id}")
        return {"message": "Scraping session deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting scraping session {session_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete scraping session")


@router.get("/sessions/{session_id}/users", response_model=List[ScrapedUserResponse])
async def get_scraped_users(
    session_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get scraped users from session."""
    try:
        # Check if session exists
        session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        if not session_result.scalar_one_or_none():
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Get scraped users
        query = select(ScrapedUser).where(ScrapedUser.session_id == session_id)
        query = query.offset(skip).limit(limit).order_by(ScrapedUser.scraped_at.desc())
        result = await db.execute(query)
        users = result.scalars().all()

        return users
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching scraped users for session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraped users")


@router.get("/sessions/{session_id}/progress")
async def get_scraping_progress(session_id: int, db: AsyncSession = Depends(get_db)):
    """Get scraping progress for session."""
    try:
        # Get session
        result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Get progress from service
        try:
            progress_data = await enhanced_facebook_scraper_service.get_scraping_progress(session_id)
        except Exception as e:
            logger.warning(f"Could not get progress from service: {e}")
            progress_data = {
                "progress": session.progress_percentage,
                "status": session.status,
                "current_step": session.current_step
            }

        return {
            "session_id": session_id,
            "status": session.status,
            "progress_percentage": session.progress_percentage,
            "current_step": session.current_step,
            "total_found": session.total_found,
            "unique_users": session.unique_users,
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "estimated_completion": None,  # Could be calculated based on progress
            **progress_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scraping progress for session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scraping progress")


@router.post("/sessions/{session_id}/start")
async def start_scraping_session(
    session_id: int,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Start scraping session with antidetect browser and saved Facebook cookies."""
    try:
        # Get session
        result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        if session.status == "running":
            raise HTTPException(status_code=400, detail="Session is already running")

        if session.status == "completed":
            raise HTTPException(status_code=400, detail="Session is already completed")

        if session.status == "failed":
            raise HTTPException(status_code=400, detail="Session has failed. Please create a new session.")

        # Get profile information
        from app.models.profile import Profile
        profile_result = await db.execute(select(Profile).where(Profile.id == session.profile_name))
        profile = profile_result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found for this session")

        # Validate profile has Facebook login
        if not profile.facebook_logged_in:
            raise HTTPException(
                status_code=400,
                detail="Profile must be logged into Facebook first. Please use 'Check' button on Profiles page to login."
            )

        # Update session status to pending
        session.status = "pending"
        session.started_at = datetime.utcnow()
        session.completed_at = None
        session.error_message = None
        session.progress_percentage = 0.0
        session.current_step = "Opening antidetect browser with saved Facebook cookies..."
        await db.commit()

        # Create config from session data
        config = {
            "post_url": session.post_url,
            "profile_id": session.profile_name,  # Use profile_id for consistency
            "scraping_types": session.scraping_types.split(",") if session.scraping_types else ["comments"],
            "include_comments": True,
            "include_likes": False,
            "include_shares": False,
            "include_reactions": False
        }

        # Start scraping in background with antidetect browser
        background_tasks.add_task(start_scraping_with_antidetect_browser, session_id, config)

        logger.info(f"Started scraping session: {session_id} with antidetect browser for profile: {profile.name}")
        return {
            "message": "Scraping session started successfully with antidetect browser",
            "session_id": session_id,
            "status": "pending",
            "profile_name": profile.name,
            "step": "Opening antidetect browser with saved Facebook cookies..."
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting scraping session {session_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to start scraping session")


@router.post("/sessions/{session_id}/stop")
async def stop_scraping_session(session_id: int, db: AsyncSession = Depends(get_db)):
    """Stop active scraping session."""
    try:
        # Get session
        result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        if session.status not in ["pending", "running"]:
            raise HTTPException(status_code=400, detail="Session is not active")

        # Update session status
        session.status = "stopped"
        session.completed_at = datetime.utcnow()
        await db.commit()

        logger.info(f"Stopped scraping session: {session_id}")
        return {"message": "Scraping session stopped successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping scraping session {session_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to stop scraping session")


@router.get("/stats", response_model=ScrapingStatsResponse)
async def get_scraping_stats(db: AsyncSession = Depends(get_db)):
    """Get scraping statistics."""
    try:
        # Get session counts
        total_sessions_result = await db.execute(select(func.count(ScrapingSession.id)))
        total_sessions = total_sessions_result.scalar()

        active_sessions_result = await db.execute(
            select(func.count(ScrapingSession.id)).where(ScrapingSession.status.in_(["pending", "running"]))
        )
        active_sessions = active_sessions_result.scalar()

        completed_sessions_result = await db.execute(
            select(func.count(ScrapingSession.id)).where(ScrapingSession.status == "completed")
        )
        completed_sessions = completed_sessions_result.scalar()

        # Get user counts
        total_users_result = await db.execute(select(func.count(ScrapedUser.id)))
        total_users_scraped = total_users_result.scalar()

        unique_users_result = await db.execute(select(func.count(func.distinct(ScrapedUser.uid))))
        unique_users = unique_users_result.scalar()

        return ScrapingStatsResponse(
            total_sessions=total_sessions,
            active_sessions=active_sessions,
            completed_sessions=completed_sessions,
            total_users_scraped=total_users_scraped,
            unique_users=unique_users
        )

    except Exception as e:
        logger.error(f"Error getting scraping stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scraping statistics")


@router.get("/users/export/{session_id}")
async def export_scraped_users(
    session_id: int,
    format: str = "json",
    db: AsyncSession = Depends(get_db)
):
    """Export scraped users data."""
    try:
        # Check if session exists
        session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = session_result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Get all scraped users
        result = await db.execute(
            select(ScrapedUser).where(ScrapedUser.session_id == session_id).order_by(ScrapedUser.scraped_at.desc())
        )
        users = result.scalars().all()

        if format.lower() == "csv":
            # Return CSV format
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow([
                "Facebook UID", "Name", "Profile URL", "Gender",
                "Location", "Comment Content", "Scraped At"
            ])

            # Write data
            for user in users:
                writer.writerow([
                    user.uid,
                    user.name,
                    user.profile_url or "",
                    user.gender.value if user.gender else "",
                    user.location or "",
                    user.comment_content or "",
                    user.scraped_at.isoformat()
                ])

            csv_content = output.getvalue()
            output.close()

            from fastapi.responses import Response
            return Response(
                content=csv_content,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=scraped_users_{session_id}.csv"}
            )

        else:
            # Return JSON format
            users_data = []
            for user in users:
                users_data.append({
                    "uid": user.uid,
                    "name": user.name,
                    "profile_url": user.profile_url,
                    "gender": user.gender.value if user.gender else None,
                    "location": user.location,
                    "comment_content": user.comment_content,
                    "scraped_at": user.scraped_at.isoformat()
                })

            return {
                "session_id": session_id,
                "session_name": session.name,
                "total_users": len(users_data),
                "exported_at": datetime.utcnow().isoformat(),
                "users": users_data
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting scraped users for session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to export scraped users")


@router.get("/sessions/{session_id}/export")
async def export_session_results(
    session_id: int,
    format: str = "xlsx",
    db: AsyncSession = Depends(get_db)
):
    """Export session results in specified format."""
    try:
        # Check if session exists
        session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
        session = session_result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")

        # Get scraped users for this session
        users_result = await db.execute(
            select(ScrapedUser).where(ScrapedUser.session_id == session_id)
        )
        users = users_result.scalars().all()

        if not users:
            raise HTTPException(status_code=404, detail="No scraped users found for this session")

        # Prepare data for export
        users_data = []
        for user in users:
            user_data = {
                "uid": user.uid,
                "name": user.name,
                "profile_url": user.profile_url,
                "scraped_at": user.scraped_at.isoformat() if user.scraped_at else None,
            }
            users_data.append(user_data)

        if format.lower() == "xlsx":
            # Create Excel file
            import pandas as pd
            from io import BytesIO
            from fastapi.responses import StreamingResponse

            df = pd.DataFrame(users_data)

            # Create Excel file in memory
            excel_buffer = BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Scraped Users', index=False)

            excel_buffer.seek(0)

            # Return as downloadable file
            filename = f"scraped_users_session_{session_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.xlsx"

            return StreamingResponse(
                BytesIO(excel_buffer.read()),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )

        elif format.lower() == "csv":
            # Create CSV file
            import pandas as pd
            from io import StringIO
            from fastapi.responses import StreamingResponse

            df = pd.DataFrame(users_data)
            csv_buffer = StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_buffer.seek(0)

            filename = f"scraped_users_session_{session_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.csv"

            return StreamingResponse(
                iter([csv_buffer.getvalue()]),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )

        else:
            # Return JSON format
            return {
                "session_id": session_id,
                "session_name": session.name,
                "total_users": len(users_data),
                "exported_at": datetime.utcnow().isoformat(),
                "users": users_data
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting session results for session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to export session results")


@router.post("/users/bulk-message")
async def bulk_message_users(
    user_ids: List[str],
    message: str,
    profile_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Send bulk messages to scraped users."""
    try:
        # Validate message
        if not message.strip():
            raise HTTPException(status_code=400, detail="Message cannot be empty")

        if len(message) > 1000:
            raise HTTPException(status_code=400, detail="Message too long (max 1000 characters)")

        # Get users by UIDs
        result = await db.execute(
            select(ScrapedUser).where(ScrapedUser.uid.in_(user_ids))
        )
        users = result.scalars().all()

        if not users:
            raise HTTPException(status_code=404, detail="No users found with provided UIDs")

        # Mock bulk messaging for now
        # In real implementation, this would integrate with Facebook messaging API

        return {
            "status": "success",
            "message": "Bulk messaging initiated (mock implementation)",
            "total_recipients": len(users),
            "message_preview": message[:100] + "..." if len(message) > 100 else message,
            "profile_id": profile_id,
            "recipients": [
                {
                    "uid": user.uid,
                    "name": user.name,
                    "status": "queued"
                }
                for user in users
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending bulk messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to send bulk messages")


# New background task function for antidetect browser workflow
async def start_scraping_with_antidetect_browser(session_id: int, config: Dict[str, Any]):
    """Background task to start scraping with antidetect browser and saved Facebook cookies."""
    try:
        logger.info(f"Starting antidetect browser scraping for session {session_id} with config: {config}")

        # Step 1: Update session status
        await _update_session_progress(session_id, {
            "progress": 5,
            "step": "Opening antidetect browser with saved Facebook cookies...",
            "status": "in_progress"
        })

        # Step 2: Use FacebookScraperService with antidetect browser
        if facebook_scraper_service:
            logger.info("Using FacebookScraperService with antidetect browser workflow")

            # Create progress callback that updates database
            async def progress_callback(progress_data):
                try:
                    logger.info(f"Antidetect browser scraping progress: {progress_data}")
                    await _update_session_progress(session_id, progress_data)
                except Exception as e:
                    logger.error(f"Error updating progress: {e}")

            # Use zendriver scraper with antidetect browser and saved cookies
            profile_id = config.get("profile_id", "default")
            logger.info(f"Using antidetect browser for profile: {profile_id}")

            result = await facebook_scraper_service.scrape_facebook_post_uids(
                profile_id=profile_id,
                post_url=config["post_url"],
                max_scroll_time=300,
                progress_callback=progress_callback
            )

        else:
            # No service available
            logger.error("FacebookScraperService not available for antidetect browser scraping")
            result = {
                "success": False,
                "error": "FacebookScraperService not initialized - cannot use antidetect browser"
            }

        logger.info(f"Antidetect browser scraping completed for session {session_id}. Success: {result.get('success', False)}")
        if result.get('success'):
            logger.info(f"Results summary: {result.get('results', {})}")

        # Update session with results
        await _update_session_with_basic_results(session_id, result)

    except Exception as e:
        logger.error(f"Error in antidetect browser scraping for session {session_id}: {str(e)}")
        await _update_session_with_basic_error(session_id, str(e))


# Original background task function (kept for backward compatibility)
async def start_scraping_task(session_id: int, config: Dict[str, Any]):
    """Background task to start scraping with enhanced zendriver integration."""
    try:
        logger.info(f"Starting scraping task for session {session_id} with config: {config}")

        # Use the new Zendriver-based scraper service if available
        if facebook_scraper_service:
            logger.info("Using FacebookScraperService (zendriver-based)")

            # Determine scraping options based on config
            scraping_types = config.get("scraping_types", ["comments"])
            if isinstance(scraping_types, str):
                scraping_types = scraping_types.split(",")

            # Map scraping types to boolean flags
            include_comments = "comments" in scraping_types
            include_likes = "likes" in scraping_types
            include_shares = "shares" in scraping_types
            include_reactions = "reactions" in scraping_types

            logger.info(f"Scraping options - Comments: {include_comments}, Likes: {include_likes}, Shares: {include_shares}, Reactions: {include_reactions}")

            # Create progress callback that updates database
            async def progress_callback(progress_data):
                try:
                    logger.info(f"Scraping progress: {progress_data}")
                    await _update_session_progress(session_id, progress_data)
                except Exception as e:
                    logger.error(f"Error updating progress: {e}")

            # Use zendriver scraper with proper profile handling
            profile_name = config.get("profile_name", "default")
            logger.info(f"Using profile: {profile_name}")
            result = await facebook_scraper_service.scrape_facebook_post_uids(
                profile_id=profile_name,
                post_url=config["post_url"],
                max_scroll_time=300,
                progress_callback=progress_callback
            )

        elif enhanced_facebook_scraper_service:
            logger.info("Using enhanced_facebook_scraper_service (fallback)")
            # Fallback to enhanced scraper service
            result = await enhanced_facebook_scraper_service.scrape_facebook_post(
                session_id=session_id,
                post_url=config["post_url"],
                scraping_types=config["scraping_types"],
                max_users=config.get("max_users"),
                include_comments=config.get("include_comments", True),
                include_likes=config.get("include_likes", False),
                include_shares=config.get("include_shares", False),
                include_reactions=config.get("include_reactions", False)
            )
        else:
            # Mock result if no service available
            logger.error("No scraping service available")
            result = {
                "success": False,
                "error": "No scraping service available - FacebookScraperService not initialized"
            }

        logger.info(f"Scraping task completed for session {session_id}. Success: {result.get('success', False)}")
        if result.get('success'):
            logger.info(f"Results summary: {result.get('results', {})}")

        # Update session with results
        await _update_session_with_basic_results(session_id, result)

    except Exception as e:
        logger.error(f"Error in scraping task for session {session_id}: {str(e)}")
        await _update_session_with_basic_error(session_id, str(e))


async def _update_session_with_basic_results(session_id: int, result: Dict[str, Any]):
    """Update session with enhanced scraping results from zendriver service"""
    try:
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as db:
            session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
            session = session_result.scalar_one_or_none()

            if session:
                if result.get("success"):
                    session.status = "completed"
                    session.completed_at = datetime.utcnow()
                    session.progress_percentage = 100.0

                    # Extract UIDs from zendriver result structure
                    results_data = result.get("results", {})
                    uids = []

                    # Handle different result structures from zendriver service
                    if "new_uids" in results_data:
                        uids = results_data["new_uids"]
                    elif "all_unique_uids" in results_data:
                        uids = results_data["all_unique_uids"]
                    elif "uids" in results_data:
                        uids = results_data["uids"]

                    # Ensure uids is a list
                    if not isinstance(uids, list):
                        uids = list(uids) if uids else []

                    # Filter valid UIDs (numeric, reasonable length)
                    valid_uids = []
                    for uid in uids:
                        uid_str = str(uid).strip()
                        if uid_str.isdigit() and 8 <= len(uid_str) <= 20:
                            valid_uids.append(uid_str)

                    session.total_found = len(valid_uids)
                    session.unique_users = len(set(valid_uids))

                    logger.info(f"Session {session_id}: Found {len(valid_uids)} valid UIDs")

                    # Add scraped users to database with deduplication
                    existing_uids = set()
                    existing_result = await db.execute(
                        select(ScrapedUser.uid).where(ScrapedUser.session_id == session_id)
                    )
                    existing_uids = {row[0] for row in existing_result.fetchall()}

                    new_users_added = 0
                    for uid in valid_uids:
                        if uid not in existing_uids:
                            scraped_user = ScrapedUser(
                                session_id=session_id,
                                uid=uid,
                                name=f"User {uid}",
                                profile_url=f"https://facebook.com/{uid}",
                                interaction_type=ScrapingType.COMMENTS,
                                scraped_at=datetime.utcnow()
                            )
                            db.add(scraped_user)
                            new_users_added += 1

                    logger.info(f"Session {session_id}: Added {new_users_added} new users to database")

                    # Store additional metadata from zendriver
                    session.current_step = "Completed"
                    if "metadata" in results_data:
                        session.error_message = None  # Clear any previous errors

                else:
                    session.status = "error"
                    session.error_message = result.get("error", "Unknown scraping error")
                    session.completed_at = datetime.utcnow()
                    session.progress_percentage = 0.0
                    logger.error(f"Session {session_id} failed: {session.error_message}")

                await db.commit()
                logger.info(f"Successfully updated session {session_id} with results")

    except Exception as e:
        logger.error(f"Error updating session {session_id} with results: {e}")
        # Try to update session with error status
        try:
            async with AsyncSessionLocal() as db:
                session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
                session = session_result.scalar_one_or_none()
                if session:
                    session.status = "error"
                    session.error_message = f"Database update error: {str(e)}"
                    session.completed_at = datetime.utcnow()
                    await db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update session {session_id} with error status: {db_error}")


async def _update_session_progress(session_id: int, progress_data: Dict[str, Any]):
    """Update session progress in database"""
    try:
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as db:
            session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
            session = session_result.scalar_one_or_none()

            if session:
                # Update progress based on progress_data structure
                if isinstance(progress_data, dict):
                    # Handle different progress data formats
                    if "progress_percentage" in progress_data:
                        session.progress_percentage = progress_data["progress_percentage"]
                    elif "percentage" in progress_data:
                        session.progress_percentage = progress_data["percentage"]
                    elif "progress" in progress_data:
                        session.progress_percentage = progress_data["progress"]

                    # Update current step if available
                    if "current_step" in progress_data:
                        session.current_step = progress_data["current_step"]
                    elif "step" in progress_data:
                        session.current_step = progress_data["step"]
                    elif "message" in progress_data:
                        session.current_step = progress_data["message"]

                    # Update status if running
                    if session.status == "pending":
                        session.status = "running"
                        session.started_at = datetime.utcnow()

                elif isinstance(progress_data, (int, float)):
                    # Simple numeric progress
                    session.progress_percentage = float(progress_data)
                    if session.status == "pending":
                        session.status = "running"
                        session.started_at = datetime.utcnow()

                await db.commit()
                logger.debug(f"Updated session {session_id} progress: {session.progress_percentage}%")

    except Exception as e:
        logger.error(f"Error updating session progress: {e}")


async def _update_session_with_basic_error(session_id: int, error_message: str):
    """Update session with error status"""
    try:
        from app.core.database import AsyncSessionLocal

        async with AsyncSessionLocal() as db:
            session_result = await db.execute(select(ScrapingSession).where(ScrapingSession.id == session_id))
            session = session_result.scalar_one_or_none()

            if session:
                session.status = "error"
                session.error_message = error_message
                session.completed_at = datetime.utcnow()
                await db.commit()
                logger.info(f"Updated session {session_id} with error: {error_message}")
    except Exception as e:
        logger.error(f"Error updating session with error: {e}")


@router.get("/test-uid-extraction")
async def test_uid_extraction(profile_url: str):
    """Test Facebook UID extraction from profile URL."""
    try:
        uid = extract_facebook_uid(profile_url)

        return {
            "profile_url": profile_url,
            "extracted_uid": uid,
            "extraction_successful": uid is not None,
            "extraction_method": "regex_pattern_matching"
        }

    except Exception as e:
        logger.error(f"Error testing UID extraction: {e}")
        raise HTTPException(status_code=500, detail="Failed to test UID extraction")


@router.get("/test-zendriver-service")
async def test_zendriver_service():
    """Test if zendriver service is available and working."""
    try:
        if not facebook_scraper_service:
            return {
                "service_available": False,
                "error": "FacebookScraperService not initialized",
                "suggestion": "Check zendriver installation and service initialization"
            }

        # Test basic service functionality
        stats = facebook_scraper_service.get_scraping_statistics()

        return {
            "service_available": True,
            "service_type": "FacebookScraperService (zendriver-based)",
            "statistics": stats,
            "message": "Zendriver service is ready for scraping"
        }

    except Exception as e:
        logger.error(f"Error testing zendriver service: {e}")
        return {
            "service_available": False,
            "error": str(e),
            "suggestion": "Check zendriver service configuration and dependencies"
        }


@router.post("/test-html-uid-extraction")
async def test_html_uid_extraction():
    """Test UID extraction from local HTML file (post_fb.html)."""
    try:
        import os
        from pathlib import Path

        # Path to the HTML file
        html_file_path = Path("post_fb.html")

        if not html_file_path.exists():
            return {
                "success": False,
                "error": "post_fb.html file not found",
                "suggestion": "Make sure post_fb.html exists in the project root"
            }

        # Read HTML content
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # Extract UIDs using the same logic as zendriver service
        if facebook_scraper_service:
            uid_extractor = facebook_scraper_service.uid_extractor
            uids = uid_extractor.extract_uids_from_text(html_content)

            # Filter valid UIDs
            valid_uids = []
            for uid in uids:
                uid_str = str(uid).strip()
                if uid_str.isdigit() and 8 <= len(uid_str) <= 20:
                    valid_uids.append(uid_str)

            return {
                "success": True,
                "html_file": str(html_file_path),
                "total_uids_found": len(uids),
                "valid_uids_count": len(valid_uids),
                "valid_uids": valid_uids[:20],  # Show first 20 UIDs
                "extraction_method": "zendriver_uid_extractor"
            }
        else:
            # Fallback manual extraction
            import re

            # Use multiple patterns to extract UIDs
            patterns = [
                r'/user/(\d{8,20})',
                r'profile\.php\?id=(\d{8,20})',
                r'fbid=(\d{8,20})',
                r'data-profileid="(\d{8,20})"',
                r'data-userid="(\d{8,20})"',
                r'"id":"(\d{8,20})"',
                r'user_id["\']:\s*["\']?(\d{8,20})',
                r'profile_id["\']:\s*["\']?(\d{8,20})'
            ]

            all_uids = set()
            for pattern in patterns:
                matches = re.findall(pattern, html_content)
                all_uids.update(matches)

            # Filter valid UIDs
            valid_uids = []
            for uid in all_uids:
                if uid.isdigit() and 8 <= len(uid) <= 20:
                    valid_uids.append(uid)

            return {
                "success": True,
                "html_file": str(html_file_path),
                "total_uids_found": len(all_uids),
                "valid_uids_count": len(valid_uids),
                "valid_uids": valid_uids[:20],  # Show first 20 UIDs
                "extraction_method": "manual_regex_patterns"
            }

    except Exception as e:
        logger.error(f"Error testing HTML UID extraction: {e}")
        return {
            "success": False,
            "error": str(e),
            "suggestion": "Check HTML file and extraction logic"
        }


# New Zendriver-based scraping endpoints

class ZendriverScrapingRequest(BaseModel):
    profile_id: str = Field(..., description="Browser profile ID to use")
    post_url: HttpUrl = Field(..., description="Facebook post URL to scrape")
    max_scroll_time: int = Field(300, description="Maximum scrolling time in seconds")

class ZendriverMultipleScrapingRequest(BaseModel):
    profile_id: str = Field(..., description="Browser profile ID to use")
    post_urls: List[HttpUrl] = Field(..., description="List of Facebook post URLs to scrape")


@router.post("/zendriver/scrape-post")
async def scrape_facebook_post_zendriver(
    request: ZendriverScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Scrape Facebook post using Zendriver with advanced features:
    - Smart scrolling with human-like behavior
    - Dynamic content loading (View more buttons)
    - Real-time deduplication
    - Antidetect browser profiles
    """
    try:
        if not facebook_scraper_service:
            raise HTTPException(
                status_code=503,
                detail="Zendriver scraper service not available"
            )

        logger.info(f"Starting Zendriver scraping for post: {request.post_url}")

        # Create scraping session in database
        scraping_session = ScrapingSession(
            name=f"Zendriver Scraping {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            post_url=str(request.post_url),
            scraping_types="comments",
            status="in_progress",
            profile_name=request.profile_id
        )
        db.add(scraping_session)
        await db.commit()
        await db.refresh(scraping_session)

        # Start scraping in background
        background_tasks.add_task(
            _perform_zendriver_scraping,
            scraping_session.id,
            request.profile_id,
            str(request.post_url),
            request.max_scroll_time,
            db
        )

        return {
            "success": True,
            "session_id": scraping_session.id,
            "message": "Zendriver scraping started",
            "profile_id": request.profile_id,
            "post_url": str(request.post_url)
        }

    except Exception as e:
        logger.error(f"Error starting Zendriver scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/zendriver/scrape-multiple")
async def scrape_multiple_posts_zendriver(
    request: ZendriverMultipleScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Scrape multiple Facebook posts using Zendriver
    """
    try:
        if not facebook_scraper_service:
            raise HTTPException(
                status_code=503,
                detail="Zendriver scraper service not available"
            )

        logger.info(f"Starting Zendriver scraping for {len(request.post_urls)} posts")

        # Create scraping session
        scraping_session = ScrapingSession(
            name=f"Multiple Zendriver Scraping {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            post_url=f"Multiple posts ({len(request.post_urls)} URLs)",
            scraping_types="comments",
            status="in_progress",
            profile_name=request.profile_id
        )
        db.add(scraping_session)
        await db.commit()
        await db.refresh(scraping_session)

        # Start scraping in background
        background_tasks.add_task(
            _perform_zendriver_multiple_scraping,
            scraping_session.id,
            request.profile_id,
            [str(url) for url in request.post_urls],
            db
        )

        return {
            "success": True,
            "session_id": scraping_session.id,
            "message": f"Zendriver scraping started for {len(request.post_urls)} posts",
            "profile_id": request.profile_id,
            "post_count": len(request.post_urls)
        }

    except Exception as e:
        logger.error(f"Error starting multiple Zendriver scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/zendriver/statistics")
async def get_zendriver_statistics():
    """Get comprehensive Zendriver scraping statistics"""
    try:
        if not facebook_scraper_service:
            raise HTTPException(
                status_code=503,
                detail="Zendriver scraper service not available"
            )

        stats = facebook_scraper_service.get_scraping_statistics()
        unique_uids_count = await facebook_scraper_service.get_unique_uids_count()

        return {
            "success": True,
            "statistics": stats,
            "total_unique_uids": unique_uids_count,
            "service_status": "active"
        }

    except Exception as e:
        logger.error(f"Error getting Zendriver statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/zendriver/export-uids")
async def export_zendriver_uids():
    """Export all unique UIDs collected by Zendriver"""
    try:
        if not facebook_scraper_service:
            raise HTTPException(
                status_code=503,
                detail="Zendriver scraper service not available"
            )

        export_file = await facebook_scraper_service.export_all_uids()

        if export_file:
            return {
                "success": True,
                "export_file": export_file,
                "message": "UIDs exported successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to export UIDs")

    except Exception as e:
        logger.error(f"Error exporting Zendriver UIDs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Background task functions

async def _perform_zendriver_scraping(
    session_id: int,
    profile_id: str,
    post_url: str,
    max_scroll_time: int,
    db: AsyncSession
):
    """Background task to perform Zendriver scraping"""
    try:
        logger.info(f"Starting background Zendriver scraping for session {session_id}")

        # Progress callback
        async def progress_callback(progress_data):
            logger.info(f"Scraping progress: {progress_data}")

        # Perform scraping
        result = await facebook_scraper_service.scrape_facebook_post_uids(
            profile_id=profile_id,
            post_url=post_url,
            max_scroll_time=max_scroll_time,
            progress_callback=progress_callback
        )

        # Update database with results
        await _update_scraping_session_with_zendriver_results(
            db, session_id, result
        )

        logger.info(f"Zendriver scraping completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in background Zendriver scraping: {e}")
        # Update session with error
        try:
            session = await db.get(ScrapingSession, session_id)
            if session:
                session.status = "failed"
                session.error_message = str(e)
                await db.commit()
        except Exception as db_error:
            logger.error(f"Error updating session with error: {db_error}")


async def _perform_zendriver_multiple_scraping(
    session_id: int,
    profile_id: str,
    post_urls: List[str],
    db: AsyncSession
):
    """Background task to perform multiple Zendriver scraping"""
    try:
        logger.info(f"Starting background multiple Zendriver scraping for session {session_id}")

        # Progress callback
        async def progress_callback(progress_data):
            logger.info(f"Multiple scraping progress: {progress_data}")

        # Perform scraping
        result = await facebook_scraper_service.scrape_multiple_posts(
            profile_id=profile_id,
            post_urls=post_urls,
            progress_callback=progress_callback
        )

        # Update database with results
        await _update_scraping_session_with_zendriver_results(
            db, session_id, result
        )

        logger.info(f"Multiple Zendriver scraping completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in background multiple Zendriver scraping: {e}")
        # Update session with error
        try:
            session = await db.get(ScrapingSession, session_id)
            if session:
                session.status = "failed"
                session.error_message = str(e)
                await db.commit()
        except Exception as db_error:
            logger.error(f"Error updating session with error: {db_error}")


async def _update_scraping_session_with_zendriver_results(
    db: AsyncSession,
    session_id: int,
    result: Dict[str, Any]
):
    """Update scraping session with Zendriver results"""
    try:
        session = await db.get(ScrapingSession, session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return

        if result["success"]:
            session.status = "completed"
            session.total_users = result["results"].get("total_uids_found", 0)
            # Fix: unique_users should be count (Integer), not list
            new_uids = result["results"].get("new_uids", [])
            session.unique_users = len(new_uids) if isinstance(new_uids, list) else 0
            session.metadata = result["results"]

            # Add scraped users to database
            new_uids = result["results"].get("new_uids", [])
            for uid in new_uids:
                scraped_user = ScrapedUser(
                    session_id=session_id,
                    uid=uid,
                    profile_url=f"https://facebook.com/{uid}",
                    interaction_type=ScrapingType.COMMENTS
                )
                db.add(scraped_user)
        else:
            session.status = "failed"
            session.error_message = result.get("error", "Unknown error")

        await db.commit()
        logger.info(f"Updated session {session_id} with Zendriver results")

    except Exception as e:
        logger.error(f"Error updating session with Zendriver results: {e}")


# Advanced Zendriver API Endpoints

class AdvancedScrapingRequest(BaseModel):
    """Advanced scraping request with optimization options"""
    profile_id: str = Field(..., description="Browser profile ID")
    post_urls: List[HttpUrl] = Field(..., description="List of Facebook post URLs")
    optimization_mode: str = Field(
        default="auto",
        description="Optimization mode: auto, parallel, memory_optimized, streaming"
    )
    max_concurrent: Optional[int] = Field(
        default=None,
        description="Maximum concurrent operations (for parallel mode)"
    )
    enable_performance_monitoring: bool = Field(
        default=True,
        description="Enable detailed performance monitoring"
    )
    session_name: Optional[str] = Field(
        default=None,
        description="Optional session name for tracking"
    )


class PerformanceReportResponse(BaseModel):
    """Performance report response"""
    timestamp: float
    system_metrics: Dict[str, Any]
    operation_summary: Dict[str, Any]
    metrics_summary: Dict[str, Any]
    performance_trends: Dict[str, Any]
    recent_anomalies: List[Dict[str, Any]]
    baseline_established: bool
    monitoring_active: bool


@router.post("/advanced-scraping", response_model=dict)
async def advanced_facebook_scraping(
    request: AdvancedScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Advanced Facebook scraping with optimization options
    """
    if not facebook_scraper_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Advanced Facebook scraper service not available"
        )

    try:
        # Create scraping session
        session = ScrapingSession(
            name=request.session_name or f"Advanced Scraping {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            scraping_type=ScrapingType.COMMENTS,
            status="running",
            total_posts=len(request.post_urls),
            metadata={
                "optimization_mode": request.optimization_mode,
                "max_concurrent": request.max_concurrent,
                "performance_monitoring": request.enable_performance_monitoring,
                "post_urls": [str(url) for url in request.post_urls]
            }
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)

        # Start background scraping based on optimization mode
        background_tasks.add_task(
            _perform_advanced_scraping,
            session.id,
            request.profile_id,
            [str(url) for url in request.post_urls],
            request.optimization_mode,
            request.max_concurrent,
            db
        )

        return {
            "success": True,
            "message": "Advanced scraping started",
            "session_id": session.id,
            "optimization_mode": request.optimization_mode,
            "estimated_time": f"{len(request.post_urls) * 30} seconds"
        }

    except Exception as e:
        logger.error(f"Error starting advanced scraping: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting advanced scraping: {str(e)}"
        )


@router.get("/performance-report", response_model=PerformanceReportResponse)
async def get_performance_report():
    """
    Get comprehensive performance report
    """
    if not facebook_scraper_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Facebook scraper service not available"
        )

    try:
        report = facebook_scraper_service.get_performance_report()
        return PerformanceReportResponse(**report)

    except Exception as e:
        logger.error(f"Error getting performance report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting performance report: {str(e)}"
        )


@router.get("/scraping-statistics", response_model=dict)
async def get_scraping_statistics():
    """
    Get comprehensive scraping statistics including performance metrics
    """
    if not facebook_scraper_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Facebook scraper service not available"
        )

    try:
        stats = facebook_scraper_service.get_scraping_statistics()
        return {
            "success": True,
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting scraping statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting scraping statistics: {str(e)}"
        )


@router.post("/parallel-scraping", response_model=dict)
async def parallel_facebook_scraping(
    request: AdvancedScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    High-performance parallel Facebook scraping
    """
    if not facebook_scraper_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Facebook scraper service not available"
        )

    try:
        # Create scraping session
        session = ScrapingSession(
            name=request.session_name or f"Parallel Scraping {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            scraping_type=ScrapingType.COMMENTS,
            status="running",
            total_posts=len(request.post_urls),
            metadata={
                "scraping_mode": "parallel",
                "max_concurrent": request.max_concurrent,
                "post_urls": [str(url) for url in request.post_urls]
            }
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)

        # Start parallel scraping
        background_tasks.add_task(
            _perform_parallel_scraping,
            session.id,
            request.profile_id,
            [str(url) for url in request.post_urls],
            request.max_concurrent,
            db
        )

        return {
            "success": True,
            "message": "Parallel scraping started",
            "session_id": session.id,
            "max_concurrent": request.max_concurrent,
            "estimated_time": f"{len(request.post_urls) * 15} seconds"
        }

    except Exception as e:
        logger.error(f"Error starting parallel scraping: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting parallel scraping: {str(e)}"
        )


@router.post("/memory-optimized-scraping", response_model=dict)
async def memory_optimized_scraping(
    request: AdvancedScrapingRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Memory-optimized Facebook scraping for large datasets
    """
    if not facebook_scraper_service:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Facebook scraper service not available"
        )

    try:
        # Create scraping session
        session = ScrapingSession(
            name=request.session_name or f"Memory Optimized Scraping {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            scraping_type=ScrapingType.COMMENTS,
            status="running",
            total_posts=len(request.post_urls),
            metadata={
                "scraping_mode": "memory_optimized",
                "streaming_enabled": True,
                "post_urls": [str(url) for url in request.post_urls]
            }
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)

        # Start memory-optimized scraping
        background_tasks.add_task(
            _perform_memory_optimized_scraping,
            session.id,
            request.profile_id,
            [str(url) for url in request.post_urls],
            db
        )

        return {
            "success": True,
            "message": "Memory-optimized scraping started",
            "session_id": session.id,
            "streaming_enabled": True,
            "estimated_time": f"{len(request.post_urls) * 25} seconds"
        }

    except Exception as e:
        logger.error(f"Error starting memory-optimized scraping: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting memory-optimized scraping: {str(e)}"
        )


# Background Task Functions for Advanced Scraping

async def _perform_advanced_scraping(
    session_id: int,
    profile_id: str,
    post_urls: List[str],
    optimization_mode: str,
    max_concurrent: Optional[int],
    db: AsyncSession
):
    """Background task for advanced scraping with different optimization modes"""
    try:
        logger.info(f"Starting advanced scraping for session {session_id} with mode: {optimization_mode}")

        # Progress callback
        async def progress_callback(progress_data):
            logger.info(f"Advanced scraping progress: {progress_data}")

        # Choose scraping method based on optimization mode
        if optimization_mode == "parallel":
            result = await facebook_scraper_service.scrape_multiple_posts_parallel(
                profile_id=profile_id,
                post_urls=post_urls,
                max_concurrent=max_concurrent,
                progress_callback=progress_callback
            )
        elif optimization_mode == "memory_optimized":
            result = await facebook_scraper_service.scrape_with_memory_optimization(
                profile_id=profile_id,
                post_urls=post_urls,
                progress_callback=progress_callback
            )
        elif optimization_mode == "streaming":
            result = await facebook_scraper_service.scrape_with_memory_optimization(
                profile_id=profile_id,
                post_urls=post_urls,
                progress_callback=progress_callback
            )
        else:  # auto mode
            result = await facebook_scraper_service.scrape_with_auto_optimization(
                profile_id=profile_id,
                post_urls=post_urls,
                progress_callback=progress_callback
            )

        # Update database with results
        await _update_scraping_session_with_advanced_results(
            db, session_id, result, optimization_mode
        )

        logger.info(f"Advanced scraping completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in advanced scraping: {e}")
        await _update_session_with_error(db, session_id, str(e))


async def _perform_parallel_scraping(
    session_id: int,
    profile_id: str,
    post_urls: List[str],
    max_concurrent: Optional[int],
    db: AsyncSession
):
    """Background task for parallel scraping"""
    try:
        logger.info(f"Starting parallel scraping for session {session_id}")

        # Progress callback
        async def progress_callback(progress_data):
            logger.info(f"Parallel scraping progress: {progress_data}")

        # Perform parallel scraping
        result = await facebook_scraper_service.scrape_multiple_posts_parallel(
            profile_id=profile_id,
            post_urls=post_urls,
            max_concurrent=max_concurrent,
            progress_callback=progress_callback
        )

        # Update database with results
        await _update_scraping_session_with_advanced_results(
            db, session_id, result, "parallel"
        )

        logger.info(f"Parallel scraping completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in parallel scraping: {e}")
        await _update_session_with_error(db, session_id, str(e))


async def _perform_memory_optimized_scraping(
    session_id: int,
    profile_id: str,
    post_urls: List[str],
    db: AsyncSession
):
    """Background task for memory-optimized scraping"""
    try:
        logger.info(f"Starting memory-optimized scraping for session {session_id}")

        # Progress callback
        async def progress_callback(progress_data):
            logger.info(f"Memory-optimized scraping progress: {progress_data}")

        # Perform memory-optimized scraping
        result = await facebook_scraper_service.scrape_with_memory_optimization(
            profile_id=profile_id,
            post_urls=post_urls,
            progress_callback=progress_callback
        )

        # Update database with results
        await _update_scraping_session_with_advanced_results(
            db, session_id, result, "memory_optimized"
        )

        logger.info(f"Memory-optimized scraping completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in memory-optimized scraping: {e}")
        await _update_session_with_error(db, session_id, str(e))


async def _update_scraping_session_with_advanced_results(
    db: AsyncSession,
    session_id: int,
    result: Dict[str, Any],
    optimization_mode: str
):
    """Update scraping session with advanced results"""
    try:
        session = await db.get(ScrapingSession, session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return

        if result["success"]:
            session.status = "completed"

            # Extract results based on structure
            results_data = result.get("results", {})

            # Handle different result structures
            if "all_unique_uids" in results_data:
                unique_uids = results_data["all_unique_uids"]
            elif "new_uids" in results_data:
                unique_uids = results_data["new_uids"]
            else:
                unique_uids = []

            session.total_users = len(unique_uids)
            # Fix: unique_users should be count (Integer), not list
            session.unique_users = len(unique_uids)

            # Enhanced metadata with optimization info
            session.metadata = {
                **results_data,
                "optimization_mode": optimization_mode,
                "scraping_method": "zendriver_advanced",
                "performance_optimized": True
            }

            # Add scraped users to database
            for uid in unique_uids:
                scraped_user = ScrapedUser(
                    session_id=session_id,
                    uid=uid,
                    profile_url=f"https://facebook.com/{uid}",
                    interaction_type=ScrapingType.COMMENTS
                )
                db.add(scraped_user)
        else:
            session.status = "failed"
            session.error_message = result.get("error", "Unknown error")

        await db.commit()
        logger.info(f"Updated session {session_id} with advanced results ({optimization_mode})")

    except Exception as e:
        logger.error(f"Error updating session with advanced results: {e}")


async def _update_session_with_error(db: AsyncSession, session_id: int, error_message: str):
    """Update session with error information"""
    try:
        session = await db.get(ScrapingSession, session_id)
        if session:
            session.status = "failed"
            session.error_message = error_message
            await db.commit()
    except Exception as db_error:
        logger.error(f"Error updating session with error: {db_error}")