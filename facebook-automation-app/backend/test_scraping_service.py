#!/usr/bin/env python3
"""
Test script to verify Facebook scraping service functionality
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def test_scraping_service():
    """Test the Facebook scraping service"""
    print("🔍 Testing Facebook Scraping Service...")
    
    try:
        # Test 1: Import FacebookScraperService
        print("\n1. Testing FacebookScraperService import...")
        from automation.facebook_scraper_service import FacebookScraperService
        print("✅ FacebookScraperService imported successfully")
        
        # Test 2: Initialize service
        print("\n2. Initializing FacebookScraperService...")
        service = FacebookScraperService()
        print("✅ FacebookScraperService initialized successfully")
        
        # Test 3: Check service statistics
        print("\n3. Getting service statistics...")
        stats = service.get_scraping_statistics()
        print(f"✅ Service statistics: {stats}")
        
        # Test 4: Test UID extraction from HTML file
        print("\n4. Testing UID extraction from post_fb.html...")
        html_file = Path("post_fb.html")
        
        if html_file.exists():
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Extract UIDs
            uids = service.uid_extractor.extract_uids_from_text(html_content)
            
            # Filter valid UIDs
            valid_uids = []
            for uid in uids:
                uid_str = str(uid).strip()
                if uid_str.isdigit() and 8 <= len(uid_str) <= 20:
                    valid_uids.append(uid_str)
            
            print(f"✅ Found {len(uids)} total UIDs, {len(valid_uids)} valid UIDs")
            print(f"   Sample valid UIDs: {valid_uids[:5]}")
        else:
            print("⚠️  post_fb.html not found - skipping HTML UID extraction test")
        
        # Test 5: Test browser manager
        print("\n5. Testing browser manager...")
        browser_available = hasattr(service.browser_manager, 'browsers')
        print(f"✅ Browser manager available: {browser_available}")
        
        print("\n🎉 All tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure zendriver is properly installed in zendriver_local/")
        return False
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_integration():
    """Test API integration"""
    print("\n🔗 Testing API Integration...")
    
    try:
        # Test API scraping module import
        print("\n1. Testing API scraping module...")
        from api.scraping import facebook_scraper_service
        
        if facebook_scraper_service:
            print("✅ FacebookScraperService available in API")
            stats = facebook_scraper_service.get_scraping_statistics()
            print(f"   Service stats: {stats}")
        else:
            print("❌ FacebookScraperService not available in API")
            return False
            
        print("\n✅ API integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ API integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Facebook Automation Service Tests")
    print("=" * 50)
    
    # Test 1: Core service functionality
    service_test = await test_scraping_service()
    
    # Test 2: API integration
    api_test = await test_api_integration()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Core Service Test: {'✅ PASS' if service_test else '❌ FAIL'}")
    print(f"   API Integration Test: {'✅ PASS' if api_test else '❌ FAIL'}")
    
    if service_test and api_test:
        print("\n🎉 All tests PASSED! The scraping service is ready to use.")
        print("\n📝 Next steps:")
        print("   1. Start the FastAPI server: uvicorn app.main:app --reload")
        print("   2. Test the scraping endpoints:")
        print("      - GET /api/scraping/test-zendriver-service")
        print("      - POST /api/scraping/test-html-uid-extraction")
        print("      - POST /api/scraping/sessions/{session_id}/start")
    else:
        print("\n❌ Some tests FAILED. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
