"""
HybridCoordinator - Orchestrates workflow between zendriver, chromedp, and colly services
"""
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, asdict
from loguru import logger
import json

from .interfaces import (
    IHybridOrchestrator, IBrowserManager, IHTMLExtractor, IHTMLParser,
    ServiceStatus, EventType, ServiceEvent, HTMLContent, UIDExtractionResult,
    ScrapingProgress, HybridSystemConfig, HybridSystemError,
    ServiceInitializationError, InterServiceCommunicationError
)
from .browser_manager import BrowserManager
from .chromedp_extractor import ChromedpExtractorManager
from .colly_parser import CollyParserManager
from .event_bus import EventBus
from .shared_memory import SharedMemoryManager


@dataclass
class ScrapingSession:
    """Scraping session data"""
    session_id: str
    profile_id: str
    post_url: str
    start_time: float
    status: str
    progress: ScrapingProgress
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class HybridCoordinator(IHybridOrchestrator):
    """Main coordinator for hybrid scraping system"""
    
    def __init__(self, config: HybridSystemConfig):
        self.config = config
        self.status = ServiceStatus.INITIALIZING
        
        # Service managers
        self.browser_manager: Optional[BrowserManager] = None
        self.chromedp_manager: Optional[ChromedpExtractorManager] = None
        self.colly_manager: Optional[CollyParserManager] = None
        
        # Communication infrastructure
        self.event_bus: Optional[EventBus] = None
        self.shared_memory: Optional[SharedMemoryManager] = None
        
        # Session management
        self.active_sessions: Dict[str, ScrapingSession] = {}
        self.session_lock = asyncio.Lock()
        
        # Performance monitoring
        self.performance_stats = {
            "total_sessions": 0,
            "successful_sessions": 0,
            "failed_sessions": 0,
            "total_uids_extracted": 0,
            "average_session_time": 0.0,
            "service_health": {}
        }
        
        # Fallback mode
        self.fallback_mode = False
        
    async def initialize_services(self) -> bool:
        """Initialize all services"""
        try:
            logger.info("Initializing HybridCoordinator services...")
            
            # Initialize communication infrastructure
            self.event_bus = EventBus()
            await self.event_bus.initialize()
            
            self.shared_memory = SharedMemoryManager(
                max_size_mb=self.config.shared_memory_size_mb
            )
            await self.shared_memory.initialize()
            
            # Initialize service managers
            self.browser_manager = BrowserManager(self.config.zendriver_config)
            if not await self.browser_manager.initialize():
                raise ServiceInitializationError("Failed to initialize BrowserManager")
            
            self.chromedp_manager = ChromedpExtractorManager(
                self.config.chromedp_config
            )
            if not await self.chromedp_manager.initialize():
                raise ServiceInitializationError("Failed to initialize ChromedpExtractor")
            
            self.colly_manager = CollyParserManager(
                self.config.colly_config
            )
            if not await self.colly_manager.initialize():
                raise ServiceInitializationError("Failed to initialize CollyParser")
            
            # Setup event handlers
            await self._setup_event_handlers()
            
            self.status = ServiceStatus.READY
            logger.info("HybridCoordinator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize HybridCoordinator: {e}")
            self.status = ServiceStatus.ERROR
            
            # Try fallback mode if enabled
            if self.config.fallback_to_legacy:
                logger.info("Attempting to initialize fallback mode...")
                return await self._initialize_fallback_mode()
            
            return False
    
    async def _initialize_fallback_mode(self) -> bool:
        """Initialize fallback mode with legacy zendriver-only"""
        try:
            # Only initialize browser manager for legacy mode
            if not self.browser_manager:
                self.browser_manager = BrowserManager(self.config.zendriver_config)
                if not await self.browser_manager.initialize():
                    return False
            
            self.fallback_mode = True
            self.status = ServiceStatus.READY
            logger.warning("HybridCoordinator running in fallback mode (zendriver-only)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize fallback mode: {e}")
            return False
    
    async def _setup_event_handlers(self):
        """Setup event handlers for inter-service communication"""
        if not self.event_bus:
            return
        
        # Browser events
        await self.event_bus.subscribe_to_events(
            [EventType.BROWSER_READY, EventType.ERROR_OCCURRED],
            self._handle_browser_events
        )
        
        # HTML extraction events
        await self.event_bus.subscribe_to_events(
            [EventType.CONTENT_LOADED],
            self._handle_extraction_events
        )
        
        # Parsing events
        await self.event_bus.subscribe_to_events(
            [EventType.PARSING_COMPLETE],
            self._handle_parsing_events
        )
    
    async def _handle_browser_events(self, event: ServiceEvent):
        """Handle browser-related events"""
        try:
            if event.event_type == EventType.BROWSER_READY:
                logger.info(f"Browser ready for profile: {event.data.get('profile_id')}")
                
            elif event.event_type == EventType.ERROR_OCCURRED:
                logger.error(f"Browser error: {event.data.get('error')}")
                await self._handle_service_error("browser", event.data)
                
        except Exception as e:
            logger.error(f"Error handling browser event: {e}")
    
    async def _handle_extraction_events(self, event: ServiceEvent):
        """Handle HTML extraction events"""
        try:
            if event.event_type == EventType.CONTENT_LOADED:
                logger.debug(f"Content loaded: {event.data.get('content_length', 0)} bytes")
                
        except Exception as e:
            logger.error(f"Error handling extraction event: {e}")
    
    async def _handle_parsing_events(self, event: ServiceEvent):
        """Handle parsing events"""
        try:
            if event.event_type == EventType.PARSING_COMPLETE:
                uids_found = event.data.get('uids_found', 0)
                logger.info(f"Parsing complete: {uids_found} UIDs found")
                
        except Exception as e:
            logger.error(f"Error handling parsing event: {e}")
    
    async def _handle_service_error(self, service_name: str, error_data: Dict[str, Any]):
        """Handle service errors with recovery attempts"""
        try:
            logger.warning(f"Service error in {service_name}: {error_data}")
            
            # Attempt service recovery based on error type
            if service_name == "browser" and not self.fallback_mode:
                # Try to restart browser manager
                if self.browser_manager:
                    await self.browser_manager.cleanup()
                    await asyncio.sleep(2)
                    await self.browser_manager.initialize()
            
            elif service_name == "chromedp" and not self.fallback_mode:
                # Switch to fallback mode if chromedp fails
                logger.warning("ChromedpExtractor failed, switching to fallback mode")
                self.fallback_mode = True
            
            elif service_name == "colly" and not self.fallback_mode:
                # Switch to fallback mode if colly fails
                logger.warning("CollyParser failed, switching to fallback mode")
                self.fallback_mode = True
                
        except Exception as e:
            logger.error(f"Error in service error handler: {e}")
    
    async def scrape_facebook_post(
        self,
        profile_id: str,
        post_url: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Main scraping workflow"""
        session_id = f"session_{int(time.time() * 1000)}"
        start_time = time.time()
        
        # Create session
        session = ScrapingSession(
            session_id=session_id,
            profile_id=profile_id,
            post_url=post_url,
            start_time=start_time,
            status="initializing",
            progress=ScrapingProgress(
                current_step="Initializing",
                progress_percentage=0.0,
                uids_found=0,
                processing_time=0.0,
                estimated_remaining=None
            )
        )
        
        async with self.session_lock:
            self.active_sessions[session_id] = session
        
        try:
            # Update progress
            await self._update_progress(session, "Initializing", 5.0, progress_callback)
            
            if self.fallback_mode:
                return await self._scrape_with_fallback(session, progress_callback)
            else:
                return await self._scrape_with_hybrid(session, progress_callback)
                
        except Exception as e:
            logger.error(f"Scraping failed for session {session_id}: {e}")
            session.error = str(e)
            session.status = "failed"
            
            # Update stats
            self.performance_stats["failed_sessions"] += 1
            
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
        
        finally:
            # Cleanup session
            async with self.session_lock:
                if session_id in self.active_sessions:
                    del self.active_sessions[session_id]
    
    async def _scrape_with_hybrid(
        self,
        session: ScrapingSession,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape using hybrid system (zendriver + chromedp + colly)"""
        try:
            # Step 1: Browser automation with zendriver
            await self._update_progress(session, "Launching browser", 10.0, progress_callback)
            
            if not await self.browser_manager.launch_browser(session.profile_id):
                raise HybridSystemError("Failed to launch browser")
            
            await self._update_progress(session, "Navigating to post", 20.0, progress_callback)
            
            if not await self.browser_manager.navigate_to_post(session.profile_id, session.post_url):
                raise HybridSystemError("Failed to navigate to post")
            
            # Step 2: Dynamic content loading
            await self._update_progress(session, "Loading dynamic content", 30.0, progress_callback)
            
            browser_info = await self.browser_manager.get_browser_info(session.profile_id)
            await self.browser_manager.perform_dynamic_loading(session.profile_id)
            
            # Step 3: HTML extraction with chromedp
            await self._update_progress(session, "Extracting HTML content", 50.0, progress_callback)
            
            html_content = await self.chromedp_manager.extract_html_with_retry(browser_info)
            
            # Store HTML in shared memory
            await self.shared_memory.store_data(
                f"html_{session.session_id}",
                html_content
            )
            
            # Step 4: UID parsing with colly
            await self._update_progress(session, "Parsing UIDs", 70.0, progress_callback)
            
            parsing_result = await self.colly_manager.parse_html_with_load_balancing(html_content)
            
            # Step 5: Results processing
            await self._update_progress(session, "Processing results", 90.0, progress_callback)
            
            processing_time = time.time() - session.start_time
            
            results = {
                "success": True,
                "session_id": session.session_id,
                "profile_id": session.profile_id,
                "post_url": session.post_url,
                "uids": parsing_result.uids,
                "total_uids": len(parsing_result.uids),
                "processing_time": processing_time,
                "extraction_method": "hybrid",
                "metadata": {
                    "html_extraction_time": html_content.metadata.get("extraction_time", 0),
                    "parsing_time": parsing_result.processing_time,
                    "content_length": len(html_content.content),
                    "deduplication_stats": parsing_result.metadata
                }
            }
            
            # Update session
            session.results = results
            session.status = "completed"
            
            # Update stats
            self.performance_stats["successful_sessions"] += 1
            self.performance_stats["total_uids_extracted"] += len(parsing_result.uids)
            
            await self._update_progress(session, "Completed", 100.0, progress_callback)
            
            return results
            
        except Exception as e:
            logger.error(f"Hybrid scraping failed: {e}")
            
            # Try fallback if enabled
            if self.config.fallback_to_legacy:
                logger.info("Attempting fallback to legacy mode...")
                self.fallback_mode = True
                return await self._scrape_with_fallback(session, progress_callback)
            
            raise e
        
        finally:
            # Cleanup browser
            if self.browser_manager:
                await self.browser_manager.close_browser(session.profile_id)
            
            # Cleanup shared memory
            if self.shared_memory:
                await self.shared_memory.delete_data(f"html_{session.session_id}")
    
    async def _scrape_with_fallback(
        self,
        session: ScrapingSession,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape using fallback mode (zendriver-only)"""
        try:
            logger.info(f"Using fallback mode for session {session.session_id}")
            
            # Import legacy scraper
            from .facebook_scraper_service import FacebookScraperService

            legacy_scraper = FacebookScraperService()

            # Use legacy scraping method
            await self._update_progress(session, "Legacy scraping", 50.0, progress_callback)

            # Create scraping request for legacy system
            scraping_request = {
                "profile_id": session.profile_id,
                "post_url": session.post_url,
                "options": {
                    "scroll_count": 5,
                    "wait_time": 2,
                    "extract_comments": True,
                    "extract_likes": True,
                    "extract_shares": True
                }
            }

            results = await legacy_scraper.scrape_facebook_post_uids(**scraping_request)
            
            processing_time = time.time() - session.start_time
            
            fallback_results = {
                "success": True,
                "session_id": session.session_id,
                "profile_id": session.profile_id,
                "post_url": session.post_url,
                "uids": results.get("uids", []),
                "total_uids": len(results.get("uids", [])),
                "processing_time": processing_time,
                "extraction_method": "fallback_zendriver",
                "metadata": results.get("metadata", {})
            }
            
            session.results = fallback_results
            session.status = "completed"
            
            # Update stats
            self.performance_stats["successful_sessions"] += 1
            self.performance_stats["total_uids_extracted"] += len(results.get("uids", []))
            
            await self._update_progress(session, "Completed", 100.0, progress_callback)
            
            return fallback_results
            
        except Exception as e:
            logger.error(f"Fallback scraping failed: {e}")
            raise e
    
    async def _update_progress(
        self,
        session: ScrapingSession,
        step: str,
        percentage: float,
        callback: Optional[Callable] = None
    ):
        """Update scraping progress"""
        try:
            processing_time = time.time() - session.start_time
            
            session.progress = ScrapingProgress(
                current_step=step,
                progress_percentage=percentage,
                uids_found=session.results.get("total_uids", 0) if session.results else 0,
                processing_time=processing_time,
                estimated_remaining=(processing_time / percentage * 100 - processing_time) if percentage > 0 else None
            )
            
            # Call progress callback if provided
            if callback:
                await callback(session.progress)
            
            # Publish progress event
            if self.event_bus:
                await self.event_bus.publish_event(ServiceEvent(
                    event_type=EventType.PROGRESS_UPDATE,
                    source_service="hybrid_coordinator",
                    target_service=None,
                    data=asdict(session.progress),
                    timestamp=time.time()
                ))
                
        except Exception as e:
            logger.error(f"Error updating progress: {e}")
    
    async def get_system_status(self) -> Dict[str, ServiceStatus]:
        """Get status of all services"""
        try:
            status = {
                "coordinator": self.status,
                "fallback_mode": self.fallback_mode
            }
            
            if self.browser_manager:
                status["browser_manager"] = ServiceStatus.READY  # Simplified
            
            if self.chromedp_manager and not self.fallback_mode:
                # Get actual status from chromedp service
                status["chromedp_extractor"] = ServiceStatus.READY  # Simplified
            
            if self.colly_manager and not self.fallback_mode:
                # Get actual status from colly service
                status["colly_parser"] = ServiceStatus.READY  # Simplified
            
            if self.event_bus:
                status["event_bus"] = ServiceStatus.READY
            
            if self.shared_memory:
                status["shared_memory"] = ServiceStatus.READY
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {"coordinator": ServiceStatus.ERROR}
    
    async def shutdown_services(self) -> bool:
        """Shutdown all services gracefully"""
        try:
            logger.info("Shutting down HybridCoordinator services...")
            
            # Cancel active sessions
            async with self.session_lock:
                for session in self.active_sessions.values():
                    session.status = "cancelled"
                self.active_sessions.clear()
            
            # Shutdown services
            if self.browser_manager:
                await self.browser_manager.cleanup()
            
            if self.chromedp_manager:
                await self.chromedp_manager.cleanup()
            
            if self.colly_manager:
                await self.colly_manager.cleanup()
            
            if self.shared_memory:
                await self.shared_memory.cleanup()
            
            if self.event_bus:
                await self.event_bus.cleanup()
            
            self.status = ServiceStatus.STOPPED
            logger.info("HybridCoordinator shutdown completed")
            return True
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            return False
