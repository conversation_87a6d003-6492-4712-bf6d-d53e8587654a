"""
ChromedpExtractor Python client for high-performance HTML extraction
"""
import asyncio
import json
import time
from typing import Dict, Any, Optional, AsyncGenerator, Callable
from dataclasses import dataclass, asdict
import aiohttp
import websockets
from loguru import logger

from .interfaces import (
    IHTMLExtractor, HTMLContent, ServiceStatus, 
    ChromedpConfig, HTMLExtractionError
)


@dataclass
class BrowserInfo:
    """Browser connection information"""
    profile_id: str
    debugger_url: str
    websocket_url: str
    tab_id: str


class ChromedpExtractorClient(IHTMLExtractor):
    """Python client for ChromedpExtractor Go service"""
    
    def __init__(self, config: ChromedpConfig, service_url: str = "http://localhost:8081"):
        self.config = config
        self.service_url = service_url
        self.status = ServiceStatus.INITIALIZING
        self.session: Optional[aiohttp.ClientSession] = None
        self.websocket_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        
    async def initialize(self) -> bool:
        """Initialize the ChromedpExtractor client"""
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.connection_timeout)
            )
            
            # Test service health
            async with self.session.get(f"{self.service_url}/health") as response:
                if response.status == 200:
                    self.status = ServiceStatus.READY
                    logger.info("ChromedpExtractor service is healthy")
                    return True
                else:
                    raise HTMLExtractionError(f"Service health check failed: {response.status}")
                    
        except Exception as e:
            logger.error(f"Failed to initialize ChromedpExtractor: {e}")
            self.status = ServiceStatus.ERROR
            return False
    
    async def connect_to_browser(self, browser_info: Dict[str, Any]) -> bool:
        """Connect to browser via CDP"""
        try:
            if not self.session:
                raise HTMLExtractionError("Client not initialized")
            
            # Convert browser_info to BrowserInfo dataclass
            browser_info_obj = BrowserInfo(**browser_info)
            
            request_data = {
                "browser_info": asdict(browser_info_obj),
                "options": {}
            }
            
            async with self.session.post(
                f"{self.service_url}/connect",
                json=request_data
            ) as response:
                result = await response.json()
                
                if result.get("success"):
                    logger.info(f"Connected to browser for profile: {browser_info_obj.profile_id}")
                    return True
                else:
                    raise HTMLExtractionError(f"Connection failed: {result.get('error')}")
                    
        except Exception as e:
            logger.error(f"Failed to connect to browser: {e}")
            self.status = ServiceStatus.ERROR
            return False
    
    async def extract_html_snapshot(self, browser_info: Dict[str, Any]) -> HTMLContent:
        """Extract complete HTML snapshot"""
        try:
            if not self.session:
                raise HTMLExtractionError("Client not initialized")
            
            browser_info_obj = BrowserInfo(**browser_info)
            
            request_data = {
                "browser_info": asdict(browser_info_obj),
                "options": {
                    "timeout": self.config.html_snapshot_timeout
                }
            }
            
            async with self.session.post(
                f"{self.service_url}/extract",
                json=request_data
            ) as response:
                result = await response.json()
                
                if result.get("success"):
                    data = result["data"]
                    return HTMLContent(
                        content=data["content"],
                        url=data["url"],
                        timestamp=data["timestamp"],
                        metadata=data["metadata"]
                    )
                else:
                    raise HTMLExtractionError(f"HTML extraction failed: {result.get('error')}")
                    
        except Exception as e:
            logger.error(f"Failed to extract HTML snapshot: {e}")
            raise HTMLExtractionError(f"HTML extraction failed: {e}")
    
    async def monitor_dom_changes(self, browser_info: Dict[str, Any]) -> AsyncGenerator[HTMLContent, None]:
        """Monitor DOM changes and yield HTML content"""
        try:
            browser_info_obj = BrowserInfo(**browser_info)
            websocket_url = f"ws://localhost:8081/monitor"
            
            async with websockets.connect(websocket_url) as websocket:
                # Send initial request
                request_data = {
                    "browser_info": asdict(browser_info_obj),
                    "options": {
                        "monitoring_interval": self.config.dom_monitoring_interval
                    }
                }
                
                await websocket.send(json.dumps(request_data))
                
                # Listen for DOM changes
                while True:
                    try:
                        message = await asyncio.wait_for(
                            websocket.recv(), 
                            timeout=self.config.connection_timeout
                        )
                        
                        result = json.loads(message)
                        
                        if result.get("success"):
                            data = result["data"]
                            yield HTMLContent(
                                content=data["content"],
                                url=data["url"],
                                timestamp=data["timestamp"],
                                metadata=data["metadata"]
                            )
                        else:
                            logger.error(f"DOM monitoring error: {result.get('error')}")
                            break
                            
                    except asyncio.TimeoutError:
                        logger.warning("DOM monitoring timeout, continuing...")
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        logger.info("WebSocket connection closed")
                        break
                        
        except Exception as e:
            logger.error(f"DOM monitoring failed: {e}")
            raise HTMLExtractionError(f"DOM monitoring failed: {e}")
    
    async def disconnect(self, profile_id: str) -> bool:
        """Disconnect from browser"""
        try:
            if not self.session:
                return True
            
            async with self.session.post(
                f"{self.service_url}/disconnect",
                params={"profile_id": profile_id}
            ) as response:
                result = await response.json()
                
                if result.get("success"):
                    logger.info(f"Disconnected from browser for profile: {profile_id}")
                    return True
                else:
                    logger.warning(f"Disconnect warning: {result}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to disconnect: {e}")
            return False
    
    async def get_service_status(self) -> ServiceStatus:
        """Get service status"""
        try:
            if not self.session:
                return ServiceStatus.ERROR
            
            async with self.session.get(f"{self.service_url}/health") as response:
                if response.status == 200:
                    return ServiceStatus.READY
                else:
                    return ServiceStatus.ERROR
                    
        except Exception as e:
            logger.error(f"Failed to get service status: {e}")
            return ServiceStatus.ERROR
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Close WebSocket connections
            for profile_id, ws in self.websocket_connections.items():
                try:
                    await ws.close()
                except:
                    pass
            self.websocket_connections.clear()
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                self.session = None
            
            self.status = ServiceStatus.STOPPED
            logger.info("ChromedpExtractor client cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")


class ChromedpExtractorManager:
    """Manager for ChromedpExtractor instances with connection pooling"""
    
    def __init__(self, config: ChromedpConfig, max_instances: int = 5):
        self.config = config
        self.max_instances = max_instances
        self.instances: Dict[str, ChromedpExtractorClient] = {}
        self.instance_pool: asyncio.Queue = asyncio.Queue(maxsize=max_instances)
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the manager and create instance pool"""
        try:
            # Create instance pool
            for i in range(self.max_instances):
                client = ChromedpExtractorClient(self.config)
                if await client.initialize():
                    await self.instance_pool.put(client)
                else:
                    logger.error(f"Failed to initialize ChromedpExtractor instance {i}")
                    return False
            
            self._initialized = True
            logger.info(f"ChromedpExtractorManager initialized with {self.max_instances} instances")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromedpExtractorManager: {e}")
            return False
    
    async def get_client(self) -> ChromedpExtractorClient:
        """Get a client from the pool"""
        if not self._initialized:
            raise HTMLExtractionError("Manager not initialized")
        
        return await self.instance_pool.get()
    
    async def return_client(self, client: ChromedpExtractorClient):
        """Return a client to the pool"""
        try:
            await self.instance_pool.put(client)
        except asyncio.QueueFull:
            # Pool is full, cleanup the client
            await client.cleanup()
    
    async def extract_html_with_retry(
        self, 
        browser_info: Dict[str, Any], 
        max_retries: int = 3
    ) -> HTMLContent:
        """Extract HTML with retry logic"""
        last_error = None
        
        for attempt in range(max_retries):
            client = await self.get_client()
            try:
                # Connect to browser if needed
                await client.connect_to_browser(browser_info)
                
                # Extract HTML
                result = await client.extract_html_snapshot(browser_info)
                await self.return_client(client)
                return result
                
            except Exception as e:
                last_error = e
                logger.warning(f"HTML extraction attempt {attempt + 1} failed: {e}")
                await client.cleanup()
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff
        
        raise HTMLExtractionError(f"HTML extraction failed after {max_retries} attempts: {last_error}")
    
    async def cleanup(self):
        """Cleanup all instances"""
        try:
            # Cleanup all instances in pool
            while not self.instance_pool.empty():
                client = await self.instance_pool.get()
                await client.cleanup()
            
            # Cleanup named instances
            for client in self.instances.values():
                await client.cleanup()
            
            self.instances.clear()
            self._initialized = False
            logger.info("ChromedpExtractorManager cleaned up")
            
        except Exception as e:
            logger.error(f"Manager cleanup error: {e}")
