"""
EventBus - Event-driven communication system for hybrid services
"""
import asyncio
import time
from typing import Dict, List, Callable, Any, Optional
from collections import defaultdict
from dataclasses import dataclass
from loguru import logger

from .interfaces import IEventBus, ServiceEvent, EventType


class EventBus(IEventBus):
    """Async event bus for inter-service communication"""
    
    def __init__(self, buffer_size: int = 1000):
        self.buffer_size = buffer_size
        self.subscribers: Dict[EventType, List[Callable]] = defaultdict(list)
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=buffer_size)
        self.running = False
        self.processor_task: Optional[asyncio.Task] = None
        self.stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_dropped": 0,
            "subscribers_count": 0,
            "processing_errors": 0
        }
        
    async def initialize(self) -> bool:
        """Initialize the event bus"""
        try:
            self.running = True
            self.processor_task = asyncio.create_task(self._process_events())
            logger.info("EventBus initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize EventBus: {e}")
            return False
    
    async def publish_event(self, event: ServiceEvent) -> bool:
        """Publish event to event bus"""
        try:
            if not self.running:
                logger.warning("EventBus not running, dropping event")
                return False
            
            # Try to put event in queue
            try:
                self.event_queue.put_nowait(event)
                self.stats["events_published"] += 1
                return True
                
            except asyncio.QueueFull:
                logger.warning(f"Event queue full, dropping event: {event.event_type}")
                self.stats["events_dropped"] += 1
                return False
                
        except Exception as e:
            logger.error(f"Error publishing event: {e}")
            return False
    
    async def subscribe_to_events(
        self, 
        event_types: List[EventType], 
        callback: Callable
    ) -> bool:
        """Subscribe to specific event types"""
        try:
            for event_type in event_types:
                if callback not in self.subscribers[event_type]:
                    self.subscribers[event_type].append(callback)
                    self.stats["subscribers_count"] += 1
            
            logger.debug(f"Subscribed to events: {[et.value for et in event_types]}")
            return True
            
        except Exception as e:
            logger.error(f"Error subscribing to events: {e}")
            return False
    
    async def unsubscribe(self, callback: Callable) -> bool:
        """Unsubscribe from events"""
        try:
            removed_count = 0
            for event_type, callbacks in self.subscribers.items():
                if callback in callbacks:
                    callbacks.remove(callback)
                    removed_count += 1
            
            self.stats["subscribers_count"] -= removed_count
            logger.debug(f"Unsubscribed from {removed_count} event types")
            return True
            
        except Exception as e:
            logger.error(f"Error unsubscribing: {e}")
            return False
    
    async def _process_events(self):
        """Process events from queue and notify subscribers"""
        logger.info("EventBus processor started")
        
        while self.running:
            try:
                # Wait for event with timeout
                try:
                    event = await asyncio.wait_for(
                        self.event_queue.get(), 
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Process event
                await self._handle_event(event)
                self.stats["events_processed"] += 1
                
                # Mark task done
                self.event_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in event processor: {e}")
                self.stats["processing_errors"] += 1
                await asyncio.sleep(0.1)  # Brief pause on error
        
        logger.info("EventBus processor stopped")
    
    async def _handle_event(self, event: ServiceEvent):
        """Handle individual event by notifying subscribers"""
        try:
            subscribers = self.subscribers.get(event.event_type, [])
            
            if not subscribers:
                logger.debug(f"No subscribers for event: {event.event_type}")
                return
            
            # Notify all subscribers concurrently
            tasks = []
            for callback in subscribers:
                task = asyncio.create_task(self._safe_callback(callback, event))
                tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"Error handling event {event.event_type}: {e}")
    
    async def _safe_callback(self, callback: Callable, event: ServiceEvent):
        """Safely execute callback with error handling"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(event)
            else:
                callback(event)
                
        except Exception as e:
            logger.error(f"Error in event callback: {e}")
            self.stats["processing_errors"] += 1
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics"""
        return {
            **self.stats,
            "queue_size": self.event_queue.qsize(),
            "running": self.running,
            "subscriber_types": {
                event_type.value: len(callbacks) 
                for event_type, callbacks in self.subscribers.items()
            }
        }
    
    async def cleanup(self):
        """Cleanup event bus resources"""
        try:
            logger.info("Cleaning up EventBus...")
            
            # Stop processing
            self.running = False
            
            # Cancel processor task
            if self.processor_task:
                self.processor_task.cancel()
                try:
                    await self.processor_task
                except asyncio.CancelledError:
                    pass
            
            # Clear subscribers
            self.subscribers.clear()
            
            # Clear remaining events
            while not self.event_queue.empty():
                try:
                    self.event_queue.get_nowait()
                    self.event_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            
            logger.info("EventBus cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during EventBus cleanup: {e}")


class EventLogger:
    """Event logger for debugging and monitoring"""
    
    def __init__(self, log_file: Optional[str] = None):
        self.log_file = log_file
        self.event_history: List[Dict[str, Any]] = []
        self.max_history = 1000
        
    async def log_event(self, event: ServiceEvent):
        """Log event for debugging"""
        try:
            event_data = {
                "timestamp": event.timestamp,
                "event_type": event.event_type.value,
                "source_service": event.source_service,
                "target_service": event.target_service,
                "data_keys": list(event.data.keys()) if event.data else []
            }
            
            # Add to history
            self.event_history.append(event_data)
            
            # Maintain history size
            if len(self.event_history) > self.max_history:
                self.event_history = self.event_history[-self.max_history:]
            
            # Log to file if specified
            if self.log_file:
                with open(self.log_file, 'a') as f:
                    f.write(f"{event_data}\n")
                    
        except Exception as e:
            logger.error(f"Error logging event: {e}")
    
    def get_recent_events(self, count: int = 50) -> List[Dict[str, Any]]:
        """Get recent events"""
        return self.event_history[-count:]
    
    def get_events_by_type(self, event_type: EventType) -> List[Dict[str, Any]]:
        """Get events by type"""
        return [
            event for event in self.event_history 
            if event["event_type"] == event_type.value
        ]


class EventMetrics:
    """Event metrics collector"""
    
    def __init__(self):
        self.metrics = {
            "events_per_second": 0.0,
            "average_processing_time": 0.0,
            "event_type_distribution": defaultdict(int),
            "service_activity": defaultdict(int),
            "error_rate": 0.0
        }
        self.last_update = time.time()
        self.event_count_window = []
        self.processing_times = []
        self.window_size = 60  # 60 seconds
        
    async def record_event(self, event: ServiceEvent, processing_time: float):
        """Record event metrics"""
        try:
            current_time = time.time()
            
            # Update event count window
            self.event_count_window.append(current_time)
            
            # Remove old events from window
            cutoff_time = current_time - self.window_size
            self.event_count_window = [
                t for t in self.event_count_window if t > cutoff_time
            ]
            
            # Update processing times
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 1000:
                self.processing_times = self.processing_times[-1000:]
            
            # Update metrics
            self.metrics["events_per_second"] = len(self.event_count_window) / self.window_size
            self.metrics["average_processing_time"] = (
                sum(self.processing_times) / len(self.processing_times)
                if self.processing_times else 0.0
            )
            self.metrics["event_type_distribution"][event.event_type.value] += 1
            self.metrics["service_activity"][event.source_service] += 1
            
        except Exception as e:
            logger.error(f"Error recording event metrics: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        return dict(self.metrics)
    
    def reset_metrics(self):
        """Reset metrics"""
        self.metrics = {
            "events_per_second": 0.0,
            "average_processing_time": 0.0,
            "event_type_distribution": defaultdict(int),
            "service_activity": defaultdict(int),
            "error_rate": 0.0
        }
        self.event_count_window.clear()
        self.processing_times.clear()


class EnhancedEventBus(EventBus):
    """Enhanced event bus with logging and metrics"""
    
    def __init__(self, buffer_size: int = 1000, enable_logging: bool = True):
        super().__init__(buffer_size)
        self.enable_logging = enable_logging
        self.event_logger = EventLogger() if enable_logging else None
        self.metrics = EventMetrics()
        
    async def _handle_event(self, event: ServiceEvent):
        """Enhanced event handling with logging and metrics"""
        start_time = time.time()
        
        try:
            # Log event if enabled
            if self.event_logger:
                await self.event_logger.log_event(event)
            
            # Handle event normally
            await super()._handle_event(event)
            
            # Record metrics
            processing_time = time.time() - start_time
            await self.metrics.record_event(event, processing_time)
            
        except Exception as e:
            logger.error(f"Error in enhanced event handling: {e}")
            processing_time = time.time() - start_time
            await self.metrics.record_event(event, processing_time)
    
    async def get_enhanced_stats(self) -> Dict[str, Any]:
        """Get enhanced statistics including metrics"""
        base_stats = await self.get_stats()
        metrics = self.metrics.get_metrics()
        
        enhanced_stats = {
            **base_stats,
            "metrics": metrics
        }
        
        if self.event_logger:
            enhanced_stats["recent_events"] = self.event_logger.get_recent_events(10)
        
        return enhanced_stats
