"""
Interface definitions for Hybrid Scraping System
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import asyncio


class ServiceStatus(Enum):
    """Service status enumeration"""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"


class EventType(Enum):
    """Event types for inter-service communication"""
    BROWSER_READY = "browser_ready"
    CONTENT_LOADED = "content_loaded"
    PARSING_COMPLETE = "parsing_complete"
    ERROR_OCCURRED = "error_occurred"
    PROGRESS_UPDATE = "progress_update"
    SHUTDOWN_REQUESTED = "shutdown_requested"


@dataclass
class ServiceEvent:
    """Event data structure"""
    event_type: EventType
    source_service: str
    target_service: Optional[str]
    data: Dict[str, Any]
    timestamp: float


@dataclass
class HTMLContent:
    """HTML content data structure"""
    content: str
    url: str
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class UIDExtractionResult:
    """UID extraction result data structure"""
    uids: List[str]
    total_found: int
    processing_time: float
    metadata: Dict[str, Any]


@dataclass
class ScrapingProgress:
    """Scraping progress data structure"""
    current_step: str
    progress_percentage: float
    uids_found: int
    processing_time: float
    estimated_remaining: Optional[float]


# Core Service Interfaces

class IBrowserManager(ABC):
    """Interface for browser management service"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize browser manager"""
        pass
    
    @abstractmethod
    async def launch_browser(self, profile_id: str) -> bool:
        """Launch browser with specific profile"""
        pass
    
    @abstractmethod
    async def navigate_to_post(self, profile_id: str, post_url: str) -> bool:
        """Navigate to Facebook post"""
        pass
    
    @abstractmethod
    async def perform_dynamic_loading(self, profile_id: str) -> Dict[str, Any]:
        """Perform dynamic content loading (scrolling, clicking)"""
        pass
    
    @abstractmethod
    async def get_browser_info(self, profile_id: str) -> Dict[str, Any]:
        """Get browser connection information"""
        pass
    
    @abstractmethod
    async def close_browser(self, profile_id: str) -> bool:
        """Close browser instance"""
        pass


class IHTMLExtractor(ABC):
    """Interface for HTML extraction service"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize HTML extractor"""
        pass
    
    @abstractmethod
    async def connect_to_browser(self, browser_info: Dict[str, Any]) -> bool:
        """Connect to browser via CDP"""
        pass
    
    @abstractmethod
    async def monitor_dom_changes(self) -> AsyncGenerator[HTMLContent, None]:
        """Monitor DOM changes and yield HTML content"""
        pass
    
    @abstractmethod
    async def extract_html_snapshot(self) -> HTMLContent:
        """Extract complete HTML snapshot"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Disconnect from browser"""
        pass


class IHTMLParser(ABC):
    """Interface for HTML parsing service"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize HTML parser"""
        pass
    
    @abstractmethod
    async def parse_html_content(self, html_content: HTMLContent) -> UIDExtractionResult:
        """Parse HTML content and extract UIDs"""
        pass
    
    @abstractmethod
    async def parse_html_stream(self, html_stream: AsyncGenerator[HTMLContent, None]) -> AsyncGenerator[UIDExtractionResult, None]:
        """Parse streaming HTML content"""
        pass
    
    @abstractmethod
    async def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get parsing performance statistics"""
        pass


class IEventBus(ABC):
    """Interface for event-driven communication"""
    
    @abstractmethod
    async def publish_event(self, event: ServiceEvent) -> bool:
        """Publish event to event bus"""
        pass
    
    @abstractmethod
    async def subscribe_to_events(self, event_types: List[EventType], callback: Callable) -> bool:
        """Subscribe to specific event types"""
        pass
    
    @abstractmethod
    async def unsubscribe(self, callback: Callable) -> bool:
        """Unsubscribe from events"""
        pass


class ISharedMemory(ABC):
    """Interface for shared memory management"""
    
    @abstractmethod
    async def store_data(self, key: str, data: Any) -> bool:
        """Store data in shared memory"""
        pass
    
    @abstractmethod
    async def retrieve_data(self, key: str) -> Optional[Any]:
        """Retrieve data from shared memory"""
        pass
    
    @abstractmethod
    async def delete_data(self, key: str) -> bool:
        """Delete data from shared memory"""
        pass
    
    @abstractmethod
    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        pass


class IHybridOrchestrator(ABC):
    """Interface for hybrid system orchestrator"""
    
    @abstractmethod
    async def initialize_services(self) -> bool:
        """Initialize all services"""
        pass
    
    @abstractmethod
    async def scrape_facebook_post(
        self, 
        profile_id: str, 
        post_url: str, 
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Main scraping workflow"""
        pass
    
    @abstractmethod
    async def get_system_status(self) -> Dict[str, ServiceStatus]:
        """Get status of all services"""
        pass
    
    @abstractmethod
    async def shutdown_services(self) -> bool:
        """Shutdown all services gracefully"""
        pass


# Configuration Data Classes

@dataclass
class ChromedpConfig:
    """Configuration for ChromedpExtractor"""
    connection_pool_size: int = 10
    connection_timeout: float = 30.0
    max_retries: int = 3
    dom_monitoring_interval: float = 1.0
    html_snapshot_timeout: float = 60.0


@dataclass
class CollyConfig:
    """Configuration for CollyParser"""
    parallel_workers: int = 8
    request_timeout: float = 30.0
    max_concurrent_requests: int = 100
    cache_enabled: bool = True
    cache_size_mb: int = 100


@dataclass
class HybridSystemConfig:
    """Configuration for entire hybrid system"""
    zendriver_config: Dict[str, Any]
    chromedp_config: ChromedpConfig
    colly_config: CollyConfig
    shared_memory_size_mb: int = 500
    event_bus_buffer_size: int = 1000
    performance_monitoring: bool = True
    fallback_to_legacy: bool = True


# Error Classes

class HybridSystemError(Exception):
    """Base exception for hybrid system"""
    pass


class ServiceInitializationError(HybridSystemError):
    """Service initialization failed"""
    pass


class InterServiceCommunicationError(HybridSystemError):
    """Inter-service communication failed"""
    pass


class HTMLExtractionError(HybridSystemError):
    """HTML extraction failed"""
    pass


class UIDParsingError(HybridSystemError):
    """UID parsing failed"""
    pass
