# Hybrid System Architecture Design

## Overview
Thiết kế kiến trúc tối ưu kết hợp zendriver, chromedp và colly để scraping Facebook UIDs với performance cao và resource efficiency.

## Current System Analysis

### Existing Components
- **FacebookScraperService**: Main orchestrator service
- **BrowserManager**: Zendriver browser management với antidetect profiles
- **FacebookUIDExtractor**: UID extraction logic
- **SmartScrollingEngine**: Intelligent scrolling
- **DynamicContentLoader**: Dynamic content handling
- **UIDDeduplicationSystem**: Duplicate removal
- **PerformanceMonitor**: System monitoring

### Current Workflow
1. Browser launch với zendriver + antidetect profile
2. Facebook authentication với saved cookies
3. Navigate to post URL
4. Smart scrolling + dynamic content loading
5. HTML extraction + UID parsing (all in Python/zendriver)
6. Deduplication + results storage

## Hybrid Architecture Design

### Core Components

#### 1. HybridScrapingOrchestrator
```python
class HybridScrapingOrchestrator:
    def __init__(self):
        self.zendriver_manager = ZendriverBrowserManager()
        self.chromedp_extractor = ChromedpExtractor()
        self.colly_parser = CollyParser()
        self.communication_layer = InterServiceCommunication()
```

#### 2. ZendriverBrowserManager (Optimized)
- **Role**: Browser automation + Authentication only
- **Responsibilities**:
  - Antidetect browser profile management
  - Facebook login với saved cookies
  - Navigation + user interactions
  - Dynamic content triggering (clicks, scrolls)
- **Optimization**: Remove HTML parsing logic

#### 3. ChromedpExtractor (New)
- **Role**: High-performance HTML extraction
- **Responsibilities**:
  - DOM monitoring với CDP
  - HTML snapshot extraction
  - Element detection + change monitoring
  - Connection pooling
- **Technology**: Go-based chromedp library

#### 4. CollyParser (New)
- **Role**: Parallel HTML parsing + UID extraction
- **Responsibilities**:
  - HTML parsing với goquery
  - Regex-based UID extraction
  - Parallel processing
  - Deduplication + caching
- **Technology**: Go-based colly framework

#### 5. InterServiceCommunication
- **Role**: Coordinate workflow between services
- **Responsibilities**:
  - Shared memory management
  - Event-driven coordination
  - Async messaging
  - Error propagation

## Data Flow Architecture

### Phase 1: Browser Automation (Zendriver)
```
ZendriverBrowserManager:
├── Launch antidetect browser
├── Load Facebook profile + cookies
├── Navigate to post URL
├── Handle authentication challenges
└── Trigger dynamic content loading
```

### Phase 2: Content Extraction (Chromedp)
```
ChromedpExtractor:
├── Connect to browser via CDP
├── Monitor DOM changes
├── Extract HTML snapshots
├── Detect content completion
└── Pass HTML to CollyParser
```

### Phase 3: Data Processing (Colly)
```
CollyParser:
├── Parse HTML với goquery
├── Apply UID extraction patterns
├── Parallel processing
├── Deduplicate results
└── Return structured data
```

## Communication Protocols

### 1. Browser State Synchronization
- Zendriver → ChromedpExtractor: Browser connection info
- ChromedpExtractor → CollyParser: HTML content streams
- CollyParser → ZendriverBrowserManager: Processing feedback

### 2. Event-Driven Coordination
```python
Events:
- BROWSER_READY: Zendriver → ChromedpExtractor
- CONTENT_LOADED: ChromedpExtractor → CollyParser
- PARSING_COMPLETE: CollyParser → Orchestrator
- ERROR_OCCURRED: Any → Orchestrator
```

### 3. Shared Memory Management
- HTML content buffer (ChromedpExtractor → CollyParser)
- UID results cache (CollyParser → Orchestrator)
- Error state sharing (All components)

## Performance Optimizations

### 1. Resource Allocation
- **Zendriver**: 1 browser instance per profile
- **Chromedp**: Connection pool (5-10 connections)
- **Colly**: Parallel workers (CPU cores * 2)

### 2. Memory Management
- Streaming HTML processing (no full DOM storage)
- Incremental UID extraction
- Automatic garbage collection

### 3. Concurrency Strategy
- Async coordination between services
- Parallel HTML parsing
- Non-blocking browser operations

## Integration Points

### 1. Existing System Integration
```python
# Modified FacebookScraperService
class FacebookScraperService:
    def __init__(self):
        self.hybrid_orchestrator = HybridScrapingOrchestrator()
        # Keep existing components for fallback
        self.legacy_components = {...}
```

### 2. API Compatibility
- Maintain existing API endpoints
- Add hybrid-specific configuration options
- Backward compatibility với current clients

### 3. Database Integration
- Same data models (ScrapingSession, ScrapedUser)
- Enhanced performance metrics
- Additional hybrid-specific statistics

## Error Handling Strategy

### 1. Component-Level Recovery
- Zendriver: Browser crash recovery
- Chromedp: CDP connection resilience
- Colly: Network error handling

### 2. System-Level Fallback
- Hybrid failure → Legacy zendriver-only mode
- Partial component failure → Graceful degradation
- Performance monitoring → Auto-optimization

## Implementation Phases

### Phase 1: ChromedpExtractor Development
- Go service development
- CDP integration
- HTML extraction logic
- Communication interface

### Phase 2: CollyParser Development  
- Go service development
- HTML parsing optimization
- UID extraction patterns
- Parallel processing

### Phase 3: Integration Layer
- InterServiceCommunication
- Event coordination
- Shared memory management
- Error handling

### Phase 4: Optimization & Testing
- Performance tuning
- Load testing
- Error recovery testing
- Production deployment

## Expected Performance Improvements

### Speed Gains
- HTML parsing: 10-50x faster (Go vs Python)
- Overall scraping: 5-10x faster
- Concurrent processing: 3-5x more posts

### Resource Efficiency
- Memory usage: 60-80% reduction
- CPU usage: 70% reduction for parsing
- Network efficiency: Better connection reuse

### Reliability
- Error recovery: 90%+ success rate
- System stability: Reduced crashes
- Scalability: Handle 100+ concurrent sessions
