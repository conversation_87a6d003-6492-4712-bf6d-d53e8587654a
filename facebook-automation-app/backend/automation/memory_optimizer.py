"""
Memory Management and Optimization System for Facebook Scraping
Handles memory-efficient processing, garbage collection, and resource optimization
"""

import gc
import psutil
import asyncio
import time
import weakref
from typing import Dict, Any, List, Optional, Set, Generator, AsyncGenerator
from dataclasses import dataclass
from loguru import logger
import threading
from collections import deque
import sys
import tracemalloc
from contextlib import asynccontextmanager


@dataclass
class MemoryStats:
    """Memory usage statistics"""
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    memory_percent: float
    process_memory_mb: float
    peak_memory_mb: float
    gc_collections: Dict[int, int]
    timestamp: float


class MemoryMonitor:
    """Real-time memory monitoring and alerting"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.monitoring = False
        self.stats_history = deque(maxlen=100)  # Keep last 100 readings
        self.peak_memory = 0.0
        self.callbacks = {
            'warning': [],
            'critical': [],
            'normal': []
        }
        
        # Start tracemalloc for detailed memory tracking
        if not tracemalloc.is_tracing():
            tracemalloc.start()
    
    def add_callback(self, level: str, callback):
        """Add callback for memory level changes"""
        if level in self.callbacks:
            self.callbacks[level].append(callback)
    
    def start_monitoring(self, interval: float = 5.0):
        """Start background memory monitoring"""
        if not self.monitoring:
            self.monitoring = True
            threading.Thread(
                target=self._monitoring_loop,
                args=(interval,),
                daemon=True
            ).start()
            logger.info("Memory monitoring started")
    
    def stop_monitoring(self):
        """Stop memory monitoring"""
        self.monitoring = False
        logger.info("Memory monitoring stopped")
    
    def _monitoring_loop(self, interval: float):
        """Background monitoring loop"""
        last_level = 'normal'
        
        while self.monitoring:
            try:
                stats = self.get_current_stats()
                self.stats_history.append(stats)
                
                # Update peak memory
                if stats.process_memory_mb > self.peak_memory:
                    self.peak_memory = stats.process_memory_mb
                
                # Determine current level
                current_level = 'normal'
                if stats.memory_percent >= self.critical_threshold:
                    current_level = 'critical'
                elif stats.memory_percent >= self.warning_threshold:
                    current_level = 'warning'
                
                # Trigger callbacks if level changed
                if current_level != last_level:
                    for callback in self.callbacks[current_level]:
                        try:
                            callback(stats)
                        except Exception as e:
                            logger.error(f"Error in memory callback: {e}")
                    last_level = current_level
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(interval)
    
    def get_current_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # Garbage collection stats
            gc_stats = {}
            for i in range(3):
                gc_stats[i] = gc.get_count()[i]
            
            return MemoryStats(
                total_memory_mb=memory.total / 1024 / 1024,
                available_memory_mb=memory.available / 1024 / 1024,
                used_memory_mb=memory.used / 1024 / 1024,
                memory_percent=memory.percent,
                process_memory_mb=process_memory.rss / 1024 / 1024,
                peak_memory_mb=self.peak_memory,
                gc_collections=gc_stats,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return MemoryStats(0, 0, 0, 0, 0, 0, {}, time.time())
    
    def get_memory_trend(self, minutes: int = 5) -> Dict[str, float]:
        """Get memory usage trend over specified minutes"""
        if not self.stats_history:
            return {"trend": 0.0, "average": 0.0, "peak": 0.0}
        
        cutoff_time = time.time() - (minutes * 60)
        recent_stats = [s for s in self.stats_history if s.timestamp >= cutoff_time]
        
        if len(recent_stats) < 2:
            return {"trend": 0.0, "average": 0.0, "peak": 0.0}
        
        # Calculate trend (slope)
        memory_values = [s.memory_percent for s in recent_stats]
        trend = (memory_values[-1] - memory_values[0]) / len(memory_values)
        
        return {
            "trend": trend,
            "average": sum(memory_values) / len(memory_values),
            "peak": max(memory_values)
        }


class StreamingProcessor:
    """Memory-efficient streaming processor for large datasets"""
    
    def __init__(self, chunk_size: int = 1000, max_memory_mb: float = 500.0):
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.processed_count = 0
        self.memory_monitor = MemoryMonitor()
    
    async def process_uids_stream(
        self,
        uid_generator: AsyncGenerator[str, None],
        processor_func,
        progress_callback: Optional = None
    ) -> AsyncGenerator[List[str], None]:
        """Process UIDs in streaming fashion to minimize memory usage"""
        try:
            chunk = []
            
            async for uid in uid_generator:
                chunk.append(uid)
                
                # Process chunk when it reaches target size or memory threshold
                if (len(chunk) >= self.chunk_size or 
                    self._should_process_chunk()):
                    
                    if chunk:
                        processed_chunk = await processor_func(chunk)
                        yield processed_chunk
                        
                        self.processed_count += len(chunk)
                        chunk.clear()
                        
                        # Force garbage collection after each chunk
                        gc.collect()
                        
                        if progress_callback:
                            await progress_callback({
                                "processed_count": self.processed_count,
                                "memory_usage": self.memory_monitor.get_current_stats().memory_percent
                            })
            
            # Process remaining items
            if chunk:
                processed_chunk = await processor_func(chunk)
                yield processed_chunk
                self.processed_count += len(chunk)
                
        except Exception as e:
            logger.error(f"Error in streaming processor: {e}")
            raise
    
    def _should_process_chunk(self) -> bool:
        """Check if chunk should be processed due to memory constraints"""
        stats = self.memory_monitor.get_current_stats()
        return stats.process_memory_mb > self.max_memory_mb
    
    async def batch_process_with_backpressure(
        self,
        items: List[Any],
        processor_func,
        max_concurrent: int = 5
    ) -> List[Any]:
        """Process items in batches with backpressure control"""
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_item(item):
            async with semaphore:
                # Check memory before processing
                if self._should_throttle():
                    await self._wait_for_memory_relief()
                
                return await processor_func(item)
        
        # Process in chunks to avoid memory buildup
        for i in range(0, len(items), self.chunk_size):
            chunk = items[i:i + self.chunk_size]
            chunk_results = await asyncio.gather(
                *[process_item(item) for item in chunk],
                return_exceptions=True
            )
            
            # Filter out exceptions and add to results
            valid_results = [r for r in chunk_results if not isinstance(r, Exception)]
            results.extend(valid_results)
            
            # Force cleanup after each chunk
            gc.collect()
            await asyncio.sleep(0.1)  # Allow other tasks to run
        
        return results
    
    def _should_throttle(self) -> bool:
        """Check if processing should be throttled due to memory pressure"""
        stats = self.memory_monitor.get_current_stats()
        return stats.memory_percent > 85.0
    
    async def _wait_for_memory_relief(self, max_wait: float = 30.0):
        """Wait for memory usage to decrease"""
        start_time = time.time()
        
        while (time.time() - start_time) < max_wait:
            if not self._should_throttle():
                break
            
            # Force garbage collection
            gc.collect()
            await asyncio.sleep(1.0)
        
        logger.info("Memory relief obtained, continuing processing")


class MemoryOptimizer:
    """Main memory optimization system"""
    
    def __init__(self):
        self.memory_monitor = MemoryMonitor()
        self.streaming_processor = StreamingProcessor()
        self.weak_references = weakref.WeakSet()
        self.optimization_stats = {
            "gc_collections_forced": 0,
            "memory_optimizations": 0,
            "peak_memory_reduced": 0,
            "objects_cleaned": 0
        }
        
        # Setup memory callbacks
        self.memory_monitor.add_callback('warning', self._on_memory_warning)
        self.memory_monitor.add_callback('critical', self._on_memory_critical)
        
        # Start monitoring
        self.memory_monitor.start_monitoring()
    
    def _on_memory_warning(self, stats: MemoryStats):
        """Handle memory warning level"""
        logger.warning(f"Memory warning: {stats.memory_percent:.1f}% used")
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self.optimize_memory_usage())
        except RuntimeError:
            # No event loop running, skip async optimization
            logger.debug("No event loop available for memory optimization")

    def _on_memory_critical(self, stats: MemoryStats):
        """Handle critical memory level"""
        logger.error(f"Critical memory usage: {stats.memory_percent:.1f}% used")
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self.emergency_memory_cleanup())
        except RuntimeError:
            # No event loop running, skip async cleanup
            logger.debug("No event loop available for emergency cleanup")
    
    async def optimize_memory_usage(self):
        """Perform memory optimization"""
        try:
            logger.info("Starting memory optimization")
            
            # Force garbage collection
            collected = gc.collect()
            self.optimization_stats["gc_collections_forced"] += 1
            self.optimization_stats["objects_cleaned"] += collected
            
            # Clear weak references to dead objects
            dead_refs = []
            for ref in self.weak_references:
                if ref() is None:
                    dead_refs.append(ref)
            
            for ref in dead_refs:
                self.weak_references.discard(ref)
            
            # Optimize deduplication system if available
            await self._optimize_deduplication_system()
            
            self.optimization_stats["memory_optimizations"] += 1
            logger.info(f"Memory optimization completed, collected {collected} objects")
            
        except Exception as e:
            logger.error(f"Error during memory optimization: {e}")
    
    async def emergency_memory_cleanup(self):
        """Emergency memory cleanup for critical situations"""
        try:
            logger.warning("Performing emergency memory cleanup")
            
            # Aggressive garbage collection
            for _ in range(3):
                collected = gc.collect()
                self.optimization_stats["objects_cleaned"] += collected
                await asyncio.sleep(0.1)
            
            # Clear all possible caches
            await self._clear_all_caches()
            
            # Force memory optimization in all components
            await self._force_component_cleanup()
            
            logger.info("Emergency memory cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during emergency cleanup: {e}")
    
    async def _optimize_deduplication_system(self):
        """Optimize deduplication system memory usage"""
        try:
            # This would be called on the actual deduplication system
            # For now, just a placeholder
            pass
        except Exception as e:
            logger.error(f"Error optimizing deduplication system: {e}")
    
    async def _clear_all_caches(self):
        """Clear all internal caches"""
        try:
            # Clear various caches
            if hasattr(sys, '_clear_type_cache'):
                sys._clear_type_cache()
            
            # Clear tracemalloc snapshots if too many
            if tracemalloc.is_tracing():
                tracemalloc.clear_traces()
            
        except Exception as e:
            logger.error(f"Error clearing caches: {e}")
    
    async def _force_component_cleanup(self):
        """Force cleanup in all registered components"""
        try:
            # This would iterate through registered components
            # and call their cleanup methods
            pass
        except Exception as e:
            logger.error(f"Error in component cleanup: {e}")
    
    @asynccontextmanager
    async def memory_managed_operation(self, operation_name: str):
        """Context manager for memory-managed operations"""
        start_stats = self.memory_monitor.get_current_stats()
        logger.info(f"Starting memory-managed operation: {operation_name}")
        
        try:
            yield
        finally:
            # Cleanup after operation
            gc.collect()
            
            end_stats = self.memory_monitor.get_current_stats()
            memory_diff = end_stats.process_memory_mb - start_stats.process_memory_mb
            
            logger.info(
                f"Operation {operation_name} completed. "
                f"Memory change: {memory_diff:+.1f}MB"
            )
            
            # If memory increased significantly, try to optimize
            if memory_diff > 50:  # 50MB threshold
                await self.optimize_memory_usage()
    
    def register_for_cleanup(self, obj):
        """Register object for cleanup tracking"""
        self.weak_references.add(obj)
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics"""
        current_stats = self.memory_monitor.get_current_stats()
        trend = self.memory_monitor.get_memory_trend()
        
        return {
            "current_memory": {
                "total_mb": current_stats.total_memory_mb,
                "available_mb": current_stats.available_memory_mb,
                "used_percent": current_stats.memory_percent,
                "process_mb": current_stats.process_memory_mb,
                "peak_mb": current_stats.peak_memory_mb
            },
            "memory_trend": trend,
            "optimization_stats": self.optimization_stats,
            "gc_stats": current_stats.gc_collections,
            "weak_references_count": len(self.weak_references)
        }
    
    async def cleanup(self):
        """Cleanup memory optimizer"""
        try:
            self.memory_monitor.stop_monitoring()
            await self.optimize_memory_usage()
            logger.info("Memory optimizer cleanup completed")
        except Exception as e:
            logger.error(f"Error during memory optimizer cleanup: {e}")


# Utility functions for memory-efficient operations

async def memory_efficient_uid_processing(
    uids: List[str],
    chunk_size: int = 1000,
    processor_func = None
) -> AsyncGenerator[List[str], None]:
    """Process UIDs in memory-efficient chunks"""
    for i in range(0, len(uids), chunk_size):
        chunk = uids[i:i + chunk_size]
        
        if processor_func:
            processed_chunk = await processor_func(chunk)
            yield processed_chunk
        else:
            yield chunk
        
        # Force garbage collection after each chunk
        gc.collect()
        await asyncio.sleep(0.01)  # Allow other tasks to run


def memory_efficient_deduplication(uids: List[str]) -> List[str]:
    """Memory-efficient deduplication using generator"""
    seen = set()
    result = []
    
    for uid in uids:
        if uid not in seen:
            seen.add(uid)
            result.append(uid)
            
            # Periodically clean up if set gets too large
            if len(seen) > 10000:
                # Keep only recent UIDs in memory
                recent_uids = result[-5000:]
                seen = set(recent_uids)
                result = recent_uids
    
    return result


class MemoryEfficientCache:
    """Memory-efficient cache with automatic cleanup"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.creation_times = {}
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        if key in self.cache:
            # Check if expired
            if time.time() - self.creation_times[key] > self.ttl:
                self._remove(key)
                return None
            
            self.access_times[key] = time.time()
            return self.cache[key]
        
        return None
    
    def set(self, key: str, value: Any):
        """Set item in cache"""
        # Clean up if at max size
        if len(self.cache) >= self.max_size:
            self._cleanup_lru()
        
        self.cache[key] = value
        self.access_times[key] = time.time()
        self.creation_times[key] = time.time()
    
    def _remove(self, key: str):
        """Remove item from cache"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.creation_times.pop(key, None)
    
    def _cleanup_lru(self):
        """Remove least recently used items"""
        if not self.access_times:
            return
        
        # Remove oldest 20% of items
        items_to_remove = max(1, len(self.cache) // 5)
        sorted_items = sorted(self.access_times.items(), key=lambda x: x[1])
        
        for key, _ in sorted_items[:items_to_remove]:
            self._remove(key)
    
    def cleanup_expired(self):
        """Remove expired items"""
        current_time = time.time()
        expired_keys = [
            key for key, creation_time in self.creation_times.items()
            if current_time - creation_time > self.ttl
        ]
        
        for key in expired_keys:
            self._remove(key)
