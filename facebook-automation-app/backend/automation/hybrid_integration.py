"""
HybridIntegration - Integration layer between HybridCoordinator and existing FacebookScraperService
"""
import asyncio
from typing import Dict, Any, Optional, Callable
from loguru import logger

from .hybrid_coordinator import HybridCoordinator
from .interfaces import HybridSystemConfig, ChromedpConfig, CollyConfig


class HybridScrapingService:
    """Enhanced FacebookScraperService with hybrid capabilities"""
    
    def __init__(self, config: Optional[HybridSystemConfig] = None):
        # Create default config if not provided
        if config is None:
            config = HybridSystemConfig(
                zendriver_config={
                    "headless": False,
                    "user_data_dir": None,
                    "proxy": None,
                    "window_size": (1920, 1080)
                },
                chromedp_config=ChromedpConfig(
                    connection_pool_size=10,
                    connection_timeout=30.0,
                    max_retries=3,
                    dom_monitoring_interval=1.0,
                    html_snapshot_timeout=60.0
                ),
                colly_config=CollyConfig(
                    parallel_workers=8,
                    request_timeout=30.0,
                    max_concurrent_requests=100,
                    cache_enabled=True,
                    cache_size_mb=100
                ),
                shared_memory_size_mb=500,
                event_bus_buffer_size=1000,
                performance_monitoring=True,
                fallback_to_legacy=True
            )
        
        self.config = config
        self.coordinator: Optional[HybridCoordinator] = None
        self.initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the hybrid scraping service"""
        try:
            if self.initialized:
                return True
            
            logger.info("Initializing HybridScrapingService...")
            
            # Create and initialize coordinator
            self.coordinator = HybridCoordinator(self.config)
            
            if await self.coordinator.initialize_services():
                self.initialized = True
                logger.info("HybridScrapingService initialized successfully")
                return True
            else:
                logger.error("Failed to initialize HybridCoordinator")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing HybridScrapingService: {e}")
            return False
    
    async def scrape_facebook_post_uids(
        self,
        profile_id: str,
        post_url: str,
        progress_callback: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Main scraping method - compatible with existing FacebookScraperService interface
        """
        try:
            if not self.initialized:
                if not await self.initialize():
                    raise Exception("Failed to initialize HybridScrapingService")
            
            if not self.coordinator:
                raise Exception("Coordinator not initialized")
            
            # Use hybrid coordinator for scraping
            result = await self.coordinator.scrape_facebook_post(
                profile_id=profile_id,
                post_url=post_url,
                progress_callback=progress_callback
            )
            
            # Transform result to match legacy interface
            return self._transform_result_to_legacy_format(result)
            
        except Exception as e:
            logger.error(f"Scraping failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "uids": [],
                "total_uids": 0,
                "processing_time": 0.0,
                "metadata": {"error": str(e)}
            }
    
    def _transform_result_to_legacy_format(self, hybrid_result: Dict[str, Any]) -> Dict[str, Any]:
        """Transform hybrid result to legacy format for backward compatibility"""
        try:
            if hybrid_result.get("success"):
                return {
                    "success": True,
                    "uids": hybrid_result.get("uids", []),
                    "total_uids": hybrid_result.get("total_uids", 0),
                    "processing_time": hybrid_result.get("processing_time", 0.0),
                    "metadata": {
                        **hybrid_result.get("metadata", {}),
                        "extraction_method": hybrid_result.get("extraction_method", "unknown"),
                        "session_id": hybrid_result.get("session_id"),
                        "profile_id": hybrid_result.get("profile_id"),
                        "post_url": hybrid_result.get("post_url")
                    }
                }
            else:
                return {
                    "success": False,
                    "error": hybrid_result.get("error", "Unknown error"),
                    "uids": [],
                    "total_uids": 0,
                    "processing_time": hybrid_result.get("processing_time", 0.0),
                    "metadata": hybrid_result.get("metadata", {})
                }
                
        except Exception as e:
            logger.error(f"Error transforming result: {e}")
            return {
                "success": False,
                "error": f"Result transformation failed: {e}",
                "uids": [],
                "total_uids": 0,
                "processing_time": 0.0,
                "metadata": {}
            }
    
    async def scrape_multiple_posts(
        self,
        scraping_requests: list,
        progress_callback: Optional[Callable] = None
    ) -> list:
        """Scrape multiple posts concurrently"""
        try:
            if not self.initialized:
                if not await self.initialize():
                    raise Exception("Failed to initialize HybridScrapingService")
            
            # Process requests concurrently
            tasks = []
            for request in scraping_requests:
                task = asyncio.create_task(
                    self.scrape_facebook_post_uids(
                        profile_id=request.get("profile_id"),
                        post_url=request.get("post_url"),
                        progress_callback=progress_callback
                    )
                )
                tasks.append(task)
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            final_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Request {i} failed: {result}")
                    final_results.append({
                        "success": False,
                        "error": str(result),
                        "uids": [],
                        "total_uids": 0,
                        "processing_time": 0.0,
                        "metadata": {"error": str(result)}
                    })
                else:
                    final_results.append(result)
            
            return final_results
            
        except Exception as e:
            logger.error(f"Multiple post scraping failed: {e}")
            return [
                {
                    "success": False,
                    "error": str(e),
                    "uids": [],
                    "total_uids": 0,
                    "processing_time": 0.0,
                    "metadata": {"error": str(e)}
                }
                for _ in scraping_requests
            ]
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        try:
            if not self.coordinator:
                return {"status": "not_initialized"}
            
            status = await self.coordinator.get_system_status()
            return {
                "hybrid_system": status,
                "initialized": self.initialized,
                "fallback_mode": getattr(self.coordinator, 'fallback_mode', False)
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        try:
            if not self.coordinator:
                return {"error": "Coordinator not initialized"}
            
            return getattr(self.coordinator, 'performance_stats', {})
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.coordinator:
                await self.coordinator.shutdown_services()
                self.coordinator = None
            
            self.initialized = False
            logger.info("HybridScrapingService cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


class HybridServiceFactory:
    """Factory for creating HybridScrapingService instances"""
    
    @staticmethod
    def create_service(
        performance_mode: str = "balanced",
        enable_fallback: bool = True,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> HybridScrapingService:
        """
        Create HybridScrapingService with predefined configurations
        
        Args:
            performance_mode: "speed", "balanced", or "memory"
            enable_fallback: Whether to enable fallback to legacy mode
            custom_config: Custom configuration overrides
        """
        
        # Base configurations for different performance modes
        configs = {
            "speed": {
                "chromedp_config": ChromedpConfig(
                    connection_pool_size=15,
                    connection_timeout=20.0,
                    max_retries=2,
                    dom_monitoring_interval=0.5,
                    html_snapshot_timeout=30.0
                ),
                "colly_config": CollyConfig(
                    parallel_workers=12,
                    request_timeout=20.0,
                    max_concurrent_requests=150,
                    cache_enabled=True,
                    cache_size_mb=200
                ),
                "shared_memory_size_mb": 1000
            },
            "balanced": {
                "chromedp_config": ChromedpConfig(
                    connection_pool_size=10,
                    connection_timeout=30.0,
                    max_retries=3,
                    dom_monitoring_interval=1.0,
                    html_snapshot_timeout=60.0
                ),
                "colly_config": CollyConfig(
                    parallel_workers=8,
                    request_timeout=30.0,
                    max_concurrent_requests=100,
                    cache_enabled=True,
                    cache_size_mb=100
                ),
                "shared_memory_size_mb": 500
            },
            "memory": {
                "chromedp_config": ChromedpConfig(
                    connection_pool_size=5,
                    connection_timeout=45.0,
                    max_retries=5,
                    dom_monitoring_interval=2.0,
                    html_snapshot_timeout=90.0
                ),
                "colly_config": CollyConfig(
                    parallel_workers=4,
                    request_timeout=45.0,
                    max_concurrent_requests=50,
                    cache_enabled=False,
                    cache_size_mb=50
                ),
                "shared_memory_size_mb": 200
            }
        }
        
        # Get base config
        base_config = configs.get(performance_mode, configs["balanced"])
        
        # Create hybrid system config
        config = HybridSystemConfig(
            zendriver_config={
                "headless": False,
                "user_data_dir": None,
                "proxy": None,
                "window_size": (1920, 1080)
            },
            chromedp_config=base_config["chromedp_config"],
            colly_config=base_config["colly_config"],
            shared_memory_size_mb=base_config["shared_memory_size_mb"],
            event_bus_buffer_size=1000,
            performance_monitoring=True,
            fallback_to_legacy=enable_fallback
        )
        
        # Apply custom config overrides
        if custom_config:
            # This is a simplified override - in production you'd want more sophisticated merging
            for key, value in custom_config.items():
                if hasattr(config, key):
                    setattr(config, key, value)
        
        return HybridScrapingService(config)
    
    @staticmethod
    def create_development_service() -> HybridScrapingService:
        """Create service optimized for development"""
        return HybridServiceFactory.create_service(
            performance_mode="memory",
            enable_fallback=True,
            custom_config={
                "performance_monitoring": True,
                "event_bus_buffer_size": 500
            }
        )
    
    @staticmethod
    def create_production_service() -> HybridScrapingService:
        """Create service optimized for production"""
        return HybridServiceFactory.create_service(
            performance_mode="speed",
            enable_fallback=True,
            custom_config={
                "performance_monitoring": True,
                "event_bus_buffer_size": 2000
            }
        )
