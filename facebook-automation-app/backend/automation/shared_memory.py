"""
SharedMemoryManager - Shared memory management for hybrid services
"""
import asyncio
import time
import pickle
import gzip
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
import sys
from loguru import logger

from .interfaces import ISharedMemory


@dataclass
class MemoryItem:
    """Memory item with metadata"""
    key: str
    data: Any
    size_bytes: int
    created_at: float
    last_accessed: float
    access_count: int
    compressed: bool = False


class SharedMemoryManager(ISharedMemory):
    """Shared memory manager for inter-service data sharing"""
    
    def __init__(self, max_size_mb: int = 500, compression_threshold_kb: int = 100):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.compression_threshold_bytes = compression_threshold_kb * 1024
        self.memory_store: Dict[str, MemoryItem] = {}
        self.current_size_bytes = 0
        self.lock = asyncio.Lock()
        
        # Statistics
        self.stats = {
            "total_items": 0,
            "current_size_mb": 0.0,
            "max_size_mb": max_size_mb,
            "hit_count": 0,
            "miss_count": 0,
            "eviction_count": 0,
            "compression_count": 0,
            "decompression_count": 0
        }
        
        # LRU tracking
        self.access_order: Dict[str, float] = {}
        
    async def initialize(self) -> bool:
        """Initialize shared memory manager"""
        try:
            logger.info(f"SharedMemoryManager initialized with {self.max_size_bytes // (1024*1024)}MB limit")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize SharedMemoryManager: {e}")
            return False
    
    async def store_data(self, key: str, data: Any) -> bool:
        """Store data in shared memory"""
        try:
            async with self.lock:
                # Serialize data
                serialized_data = pickle.dumps(data)
                data_size = len(serialized_data)
                
                # Compress if data is large
                compressed = False
                if data_size > self.compression_threshold_bytes:
                    try:
                        compressed_data = gzip.compress(serialized_data)
                        if len(compressed_data) < data_size:
                            serialized_data = compressed_data
                            compressed = True
                            self.stats["compression_count"] += 1
                    except Exception as e:
                        logger.warning(f"Compression failed for key {key}: {e}")
                
                final_size = len(serialized_data)
                
                # Check if we need to evict items
                await self._ensure_space(final_size)
                
                # Remove existing item if it exists
                if key in self.memory_store:
                    old_item = self.memory_store[key]
                    self.current_size_bytes -= old_item.size_bytes
                
                # Create new memory item
                current_time = time.time()
                memory_item = MemoryItem(
                    key=key,
                    data=serialized_data,
                    size_bytes=final_size,
                    created_at=current_time,
                    last_accessed=current_time,
                    access_count=0,
                    compressed=compressed
                )
                
                # Store item
                self.memory_store[key] = memory_item
                self.current_size_bytes += final_size
                self.access_order[key] = current_time
                
                # Update stats
                self.stats["total_items"] = len(self.memory_store)
                self.stats["current_size_mb"] = self.current_size_bytes / (1024 * 1024)
                
                logger.debug(f"Stored data for key {key}: {final_size} bytes (compressed: {compressed})")
                return True
                
        except Exception as e:
            logger.error(f"Error storing data for key {key}: {e}")
            return False
    
    async def retrieve_data(self, key: str) -> Optional[Any]:
        """Retrieve data from shared memory"""
        try:
            async with self.lock:
                if key not in self.memory_store:
                    self.stats["miss_count"] += 1
                    return None
                
                memory_item = self.memory_store[key]
                
                # Update access info
                current_time = time.time()
                memory_item.last_accessed = current_time
                memory_item.access_count += 1
                self.access_order[key] = current_time
                
                # Deserialize data
                serialized_data = memory_item.data
                
                # Decompress if needed
                if memory_item.compressed:
                    try:
                        serialized_data = gzip.decompress(serialized_data)
                        self.stats["decompression_count"] += 1
                    except Exception as e:
                        logger.error(f"Decompression failed for key {key}: {e}")
                        return None
                
                # Deserialize
                data = pickle.loads(serialized_data)
                
                self.stats["hit_count"] += 1
                logger.debug(f"Retrieved data for key {key}: {memory_item.size_bytes} bytes")
                return data
                
        except Exception as e:
            logger.error(f"Error retrieving data for key {key}: {e}")
            self.stats["miss_count"] += 1
            return None
    
    async def delete_data(self, key: str) -> bool:
        """Delete data from shared memory"""
        try:
            async with self.lock:
                if key not in self.memory_store:
                    return False
                
                memory_item = self.memory_store[key]
                self.current_size_bytes -= memory_item.size_bytes
                
                del self.memory_store[key]
                if key in self.access_order:
                    del self.access_order[key]
                
                # Update stats
                self.stats["total_items"] = len(self.memory_store)
                self.stats["current_size_mb"] = self.current_size_bytes / (1024 * 1024)
                
                logger.debug(f"Deleted data for key {key}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting data for key {key}: {e}")
            return False
    
    async def _ensure_space(self, required_bytes: int):
        """Ensure enough space by evicting LRU items if necessary"""
        try:
            # Check if we have enough space
            if self.current_size_bytes + required_bytes <= self.max_size_bytes:
                return
            
            logger.info(f"Need to evict items to make space for {required_bytes} bytes")
            
            # Sort items by last access time (LRU first)
            sorted_keys = sorted(
                self.access_order.keys(),
                key=lambda k: self.access_order[k]
            )
            
            # Evict items until we have enough space
            for key in sorted_keys:
                if self.current_size_bytes + required_bytes <= self.max_size_bytes:
                    break
                
                memory_item = self.memory_store[key]
                self.current_size_bytes -= memory_item.size_bytes
                
                del self.memory_store[key]
                del self.access_order[key]
                
                self.stats["eviction_count"] += 1
                logger.debug(f"Evicted item {key} ({memory_item.size_bytes} bytes)")
            
            # Update stats
            self.stats["total_items"] = len(self.memory_store)
            self.stats["current_size_mb"] = self.current_size_bytes / (1024 * 1024)
            
        except Exception as e:
            logger.error(f"Error ensuring space: {e}")
    
    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        try:
            async with self.lock:
                # Calculate additional metrics
                hit_rate = (
                    self.stats["hit_count"] / (self.stats["hit_count"] + self.stats["miss_count"])
                    if (self.stats["hit_count"] + self.stats["miss_count"]) > 0 else 0.0
                )
                
                usage_percentage = (self.current_size_bytes / self.max_size_bytes) * 100
                
                # Get item size distribution
                size_distribution = {}
                for item in self.memory_store.values():
                    size_range = self._get_size_range(item.size_bytes)
                    size_distribution[size_range] = size_distribution.get(size_range, 0) + 1
                
                # Get most accessed items
                most_accessed = sorted(
                    self.memory_store.items(),
                    key=lambda x: x[1].access_count,
                    reverse=True
                )[:5]
                
                return {
                    **self.stats,
                    "hit_rate": hit_rate,
                    "usage_percentage": usage_percentage,
                    "average_item_size_kb": (
                        self.current_size_bytes / len(self.memory_store) / 1024
                        if self.memory_store else 0
                    ),
                    "size_distribution": size_distribution,
                    "most_accessed_items": [
                        {
                            "key": key,
                            "size_kb": item.size_bytes / 1024,
                            "access_count": item.access_count,
                            "compressed": item.compressed
                        }
                        for key, item in most_accessed
                    ]
                }
                
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return self.stats
    
    def _get_size_range(self, size_bytes: int) -> str:
        """Get size range category for statistics"""
        if size_bytes < 1024:
            return "< 1KB"
        elif size_bytes < 10 * 1024:
            return "1-10KB"
        elif size_bytes < 100 * 1024:
            return "10-100KB"
        elif size_bytes < 1024 * 1024:
            return "100KB-1MB"
        elif size_bytes < 10 * 1024 * 1024:
            return "1-10MB"
        else:
            return "> 10MB"
    
    async def clear_all(self) -> bool:
        """Clear all data from shared memory"""
        try:
            async with self.lock:
                self.memory_store.clear()
                self.access_order.clear()
                self.current_size_bytes = 0
                
                # Reset stats
                self.stats["total_items"] = 0
                self.stats["current_size_mb"] = 0.0
                
                logger.info("Cleared all shared memory data")
                return True
                
        except Exception as e:
            logger.error(f"Error clearing shared memory: {e}")
            return False
    
    async def get_keys(self) -> list:
        """Get all keys in shared memory"""
        try:
            async with self.lock:
                return list(self.memory_store.keys())
                
        except Exception as e:
            logger.error(f"Error getting keys: {e}")
            return []
    
    async def get_item_info(self, key: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific item"""
        try:
            async with self.lock:
                if key not in self.memory_store:
                    return None
                
                item = self.memory_store[key]
                return {
                    "key": item.key,
                    "size_bytes": item.size_bytes,
                    "size_kb": item.size_bytes / 1024,
                    "created_at": item.created_at,
                    "last_accessed": item.last_accessed,
                    "access_count": item.access_count,
                    "compressed": item.compressed,
                    "age_seconds": time.time() - item.created_at
                }
                
        except Exception as e:
            logger.error(f"Error getting item info for {key}: {e}")
            return None
    
    async def cleanup(self):
        """Cleanup shared memory resources"""
        try:
            await self.clear_all()
            logger.info("SharedMemoryManager cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during SharedMemoryManager cleanup: {e}")


class MemoryMonitor:
    """Monitor for shared memory usage and performance"""
    
    def __init__(self, shared_memory: SharedMemoryManager, check_interval: float = 30.0):
        self.shared_memory = shared_memory
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.alerts = []
        
    async def start_monitoring(self):
        """Start memory monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Memory monitoring started")
    
    async def stop_monitoring(self):
        """Stop memory monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Memory monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                usage = await self.shared_memory.get_memory_usage()
                
                # Check for alerts
                await self._check_alerts(usage)
                
                # Log periodic stats
                logger.debug(f"Memory usage: {usage['current_size_mb']:.1f}MB "
                           f"({usage.get('usage_percentage', 0):.1f}%), "
                           f"Hit rate: {usage.get('hit_rate', 0):.2f}")
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_alerts(self, usage: Dict[str, Any]):
        """Check for memory usage alerts"""
        try:
            usage_percentage = usage.get('usage_percentage', 0)
            hit_rate = usage.get('hit_rate', 1.0)
            
            # High memory usage alert
            if usage_percentage > 90:
                alert = f"High memory usage: {usage_percentage:.1f}%"
                if alert not in self.alerts:
                    self.alerts.append(alert)
                    logger.warning(alert)
            
            # Low hit rate alert
            if hit_rate < 0.5 and usage['hit_count'] + usage['miss_count'] > 100:
                alert = f"Low cache hit rate: {hit_rate:.2f}"
                if alert not in self.alerts:
                    self.alerts.append(alert)
                    logger.warning(alert)
            
            # Keep only recent alerts
            if len(self.alerts) > 10:
                self.alerts = self.alerts[-10:]
                
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
    
    def get_alerts(self) -> List[str]:
        """Get current alerts"""
        return self.alerts.copy()
