"""
HybridDemo - Demo script for testing hybrid scraping system
"""
import asyncio
import time
from typing import Dict, Any
from loguru import logger

from .hybrid_integration import HybridScrapingService, HybridServiceFactory


class HybridSystemDemo:
    """Demo class for hybrid scraping system"""
    
    def __init__(self):
        self.service: HybridScrapingService = None
        
    async def run_basic_demo(self):
        """Run basic demo of hybrid system"""
        logger.info("=== Hybrid Scraping System Demo ===")
        
        try:
            # Create service
            logger.info("Creating HybridScrapingService...")
            self.service = HybridServiceFactory.create_development_service()
            
            # Initialize service
            logger.info("Initializing service...")
            if not await self.service.initialize():
                logger.error("Failed to initialize service")
                return
            
            # Get system status
            logger.info("Getting system status...")
            status = await self.service.get_system_status()
            logger.info(f"System status: {status}")
            
            # Demo scraping (using test data since we don't have real Facebook access)
            await self._demo_scraping()
            
            # Show performance stats
            await self._show_performance_stats()
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
        
        finally:
            if self.service:
                await self.service.cleanup()
    
    async def _demo_scraping(self):
        """Demo scraping functionality"""
        logger.info("\n=== Demo Scraping ===")
        
        # Test data
        test_profile_id = "test_profile_001"
        test_post_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890"
        
        # Progress callback
        async def progress_callback(progress):
            logger.info(f"Progress: {progress.current_step} - {progress.progress_percentage:.1f}%")
        
        try:
            logger.info(f"Starting scraping for profile: {test_profile_id}")
            logger.info(f"Post URL: {test_post_url}")
            
            start_time = time.time()
            
            # Perform scraping
            result = await self.service.scrape_facebook_post_uids(
                profile_id=test_profile_id,
                post_url=test_post_url,
                progress_callback=progress_callback
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Display results
            logger.info(f"\n=== Scraping Results ===")
            logger.info(f"Success: {result.get('success', False)}")
            logger.info(f"Total UIDs: {result.get('total_uids', 0)}")
            logger.info(f"Processing time: {processing_time:.2f}s")
            logger.info(f"Extraction method: {result.get('metadata', {}).get('extraction_method', 'unknown')}")
            
            if result.get('success'):
                uids = result.get('uids', [])
                if uids:
                    logger.info(f"Sample UIDs: {uids[:5]}...")  # Show first 5 UIDs
                else:
                    logger.info("No UIDs extracted (expected in demo mode)")
            else:
                logger.error(f"Scraping failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Demo scraping failed: {e}")
    
    async def _show_performance_stats(self):
        """Show performance statistics"""
        logger.info("\n=== Performance Statistics ===")
        
        try:
            stats = await self.service.get_performance_stats()
            
            logger.info(f"Total sessions: {stats.get('total_sessions', 0)}")
            logger.info(f"Successful sessions: {stats.get('successful_sessions', 0)}")
            logger.info(f"Failed sessions: {stats.get('failed_sessions', 0)}")
            logger.info(f"Total UIDs extracted: {stats.get('total_uids_extracted', 0)}")
            logger.info(f"Average session time: {stats.get('average_session_time', 0):.2f}s")
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
    
    async def run_performance_comparison(self):
        """Run performance comparison between different modes"""
        logger.info("\n=== Performance Comparison Demo ===")
        
        modes = ["memory", "balanced", "speed"]
        results = {}
        
        for mode in modes:
            logger.info(f"\nTesting {mode} mode...")
            
            try:
                # Create service for this mode
                service = HybridServiceFactory.create_service(
                    performance_mode=mode,
                    enable_fallback=True
                )
                
                # Initialize
                if not await service.initialize():
                    logger.error(f"Failed to initialize {mode} mode")
                    continue
                
                # Test scraping
                start_time = time.time()
                
                result = await service.scrape_facebook_post_uids(
                    profile_id="test_profile_comparison",
                    post_url="https://www.facebook.com/groups/test/posts/comparison"
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                results[mode] = {
                    "processing_time": processing_time,
                    "success": result.get("success", False),
                    "extraction_method": result.get("metadata", {}).get("extraction_method", "unknown")
                }
                
                logger.info(f"{mode} mode: {processing_time:.2f}s, "
                           f"Success: {result.get('success', False)}, "
                           f"Method: {result.get('metadata', {}).get('extraction_method', 'unknown')}")
                
                # Cleanup
                await service.cleanup()
                
            except Exception as e:
                logger.error(f"Error testing {mode} mode: {e}")
                results[mode] = {"error": str(e)}
        
        # Show comparison
        logger.info("\n=== Performance Comparison Results ===")
        for mode, result in results.items():
            if "error" in result:
                logger.info(f"{mode}: ERROR - {result['error']}")
            else:
                logger.info(f"{mode}: {result['processing_time']:.2f}s, "
                           f"Method: {result['extraction_method']}")
    
    async def run_concurrent_demo(self):
        """Demo concurrent scraping"""
        logger.info("\n=== Concurrent Scraping Demo ===")
        
        try:
            # Create service
            service = HybridServiceFactory.create_service(
                performance_mode="balanced",
                enable_fallback=True
            )
            
            if not await service.initialize():
                logger.error("Failed to initialize service for concurrent demo")
                return
            
            # Create multiple scraping requests
            requests = [
                {
                    "profile_id": f"test_profile_{i:03d}",
                    "post_url": f"https://www.facebook.com/groups/test/posts/{i}"
                }
                for i in range(1, 6)  # 5 concurrent requests
            ]
            
            logger.info(f"Starting {len(requests)} concurrent scraping requests...")
            
            start_time = time.time()
            
            # Process requests concurrently
            results = await service.scrape_multiple_posts(requests)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Show results
            logger.info(f"\n=== Concurrent Scraping Results ===")
            logger.info(f"Total time: {total_time:.2f}s")
            logger.info(f"Average time per request: {total_time / len(requests):.2f}s")
            
            successful = sum(1 for r in results if r.get("success", False))
            logger.info(f"Successful requests: {successful}/{len(requests)}")
            
            for i, result in enumerate(results):
                status = "SUCCESS" if result.get("success", False) else "FAILED"
                method = result.get("metadata", {}).get("extraction_method", "unknown")
                logger.info(f"Request {i+1}: {status}, Method: {method}")
            
            await service.cleanup()
            
        except Exception as e:
            logger.error(f"Concurrent demo failed: {e}")


async def main():
    """Main demo function"""
    demo = HybridSystemDemo()
    
    try:
        # Run basic demo
        await demo.run_basic_demo()
        
        # Wait a bit between demos
        await asyncio.sleep(2)
        
        # Run performance comparison
        await demo.run_performance_comparison()
        
        # Wait a bit between demos
        await asyncio.sleep(2)
        
        # Run concurrent demo
        await demo.run_concurrent_demo()
        
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
    
    logger.info("Demo completed")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run demo
    asyncio.run(main())


class HybridSystemTester:
    """Comprehensive tester for hybrid system"""
    
    def __init__(self):
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all system tests"""
        logger.info("=== Hybrid System Comprehensive Tests ===")
        
        tests = [
            ("Service Initialization", self._test_service_initialization),
            ("System Status", self._test_system_status),
            ("Configuration Modes", self._test_configuration_modes),
            ("Error Handling", self._test_error_handling),
            ("Resource Management", self._test_resource_management)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\nRunning test: {test_name}")
            try:
                result = await test_func()
                self.test_results.append({
                    "test": test_name,
                    "status": "PASSED" if result else "FAILED",
                    "result": result
                })
                logger.info(f"Test {test_name}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                logger.error(f"Test {test_name} failed with exception: {e}")
                self.test_results.append({
                    "test": test_name,
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Show summary
        self._show_test_summary()
    
    async def _test_service_initialization(self) -> bool:
        """Test service initialization"""
        try:
            service = HybridServiceFactory.create_development_service()
            result = await service.initialize()
            await service.cleanup()
            return result
        except Exception as e:
            logger.error(f"Service initialization test failed: {e}")
            return False
    
    async def _test_system_status(self) -> bool:
        """Test system status retrieval"""
        try:
            service = HybridServiceFactory.create_development_service()
            await service.initialize()
            
            status = await service.get_system_status()
            result = isinstance(status, dict) and "hybrid_system" in status
            
            await service.cleanup()
            return result
        except Exception as e:
            logger.error(f"System status test failed: {e}")
            return False
    
    async def _test_configuration_modes(self) -> bool:
        """Test different configuration modes"""
        try:
            modes = ["memory", "balanced", "speed"]
            
            for mode in modes:
                service = HybridServiceFactory.create_service(performance_mode=mode)
                if not await service.initialize():
                    await service.cleanup()
                    return False
                await service.cleanup()
            
            return True
        except Exception as e:
            logger.error(f"Configuration modes test failed: {e}")
            return False
    
    async def _test_error_handling(self) -> bool:
        """Test error handling"""
        try:
            service = HybridServiceFactory.create_development_service()
            await service.initialize()
            
            # Test with invalid parameters
            result = await service.scrape_facebook_post_uids(
                profile_id="",  # Invalid profile ID
                post_url="invalid_url"  # Invalid URL
            )
            
            # Should return error result, not raise exception
            is_error_handled = not result.get("success", True)
            
            await service.cleanup()
            return is_error_handled
        except Exception as e:
            logger.error(f"Error handling test failed: {e}")
            return False
    
    async def _test_resource_management(self) -> bool:
        """Test resource management"""
        try:
            service = HybridServiceFactory.create_development_service()
            await service.initialize()
            
            # Get initial stats
            initial_stats = await service.get_performance_stats()
            
            # Perform cleanup
            await service.cleanup()
            
            # Check if cleanup was successful (no exceptions)
            return True
        except Exception as e:
            logger.error(f"Resource management test failed: {e}")
            return False
    
    def _show_test_summary(self):
        """Show test summary"""
        logger.info("\n=== Test Summary ===")
        
        passed = sum(1 for r in self.test_results if r["status"] == "PASSED")
        failed = sum(1 for r in self.test_results if r["status"] == "FAILED")
        errors = sum(1 for r in self.test_results if r["status"] == "ERROR")
        
        logger.info(f"Total tests: {len(self.test_results)}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Errors: {errors}")
        
        if failed > 0 or errors > 0:
            logger.info("\nFailed/Error tests:")
            for result in self.test_results:
                if result["status"] in ["FAILED", "ERROR"]:
                    logger.info(f"- {result['test']}: {result['status']}")
                    if "error" in result:
                        logger.info(f"  Error: {result['error']}")


async def run_tests():
    """Run comprehensive tests"""
    tester = HybridSystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        asyncio.run(run_tests())
    else:
        asyncio.run(main())
