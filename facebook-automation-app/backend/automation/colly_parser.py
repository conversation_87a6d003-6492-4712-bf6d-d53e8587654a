"""
CollyParser Python client for high-performance HTML parsing and UID extraction
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass, asdict
import aiohttp
from loguru import logger

from .interfaces import (
    IHTMLParser, HTMLContent, UIDExtractionResult, ServiceStatus,
    CollyConfig, UIDParsingError
)


class CollyParserClient(IHTMLParser):
    """Python client for CollyParser Go service"""
    
    def __init__(self, config: CollyConfig, service_url: str = "http://localhost:8082"):
        self.config = config
        self.service_url = service_url
        self.status = ServiceStatus.INITIALIZING
        self.session: Optional[aiohttp.ClientSession] = None
        self.stats = {
            "total_requests": 0,
            "total_uids": 0,
            "total_processing_time": 0.0,
            "cache_hits": 0
        }
        
    async def initialize(self) -> bool:
        """Initialize the CollyParser client"""
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
            )
            
            # Test service health
            async with self.session.get(f"{self.service_url}/health") as response:
                if response.status == 200:
                    self.status = ServiceStatus.READY
                    logger.info("CollyParser service is healthy")
                    return True
                else:
                    raise UIDParsingError(f"Service health check failed: {response.status}")
                    
        except Exception as e:
            logger.error(f"Failed to initialize CollyParser: {e}")
            self.status = ServiceStatus.ERROR
            return False
    
    async def parse_html_content(self, html_content: HTMLContent) -> UIDExtractionResult:
        """Parse HTML content and extract UIDs"""
        try:
            if not self.session:
                raise UIDParsingError("Client not initialized")
            
            request_data = {
                "html_content": {
                    "content": html_content.content,
                    "url": html_content.url,
                    "timestamp": html_content.timestamp,
                    "metadata": html_content.metadata
                },
                "options": {
                    "parallel_workers": self.config.parallel_workers,
                    "cache_enabled": self.config.cache_enabled
                }
            }
            
            start_time = time.time()
            
            async with self.session.post(
                f"{self.service_url}/parse",
                json=request_data
            ) as response:
                result = await response.json()
                
                processing_time = time.time() - start_time
                
                if result.get("success"):
                    data = result["data"]
                    
                    # Update local stats
                    self.stats["total_requests"] += 1
                    self.stats["total_uids"] += data["total_found"]
                    self.stats["total_processing_time"] += processing_time
                    
                    return UIDExtractionResult(
                        uids=data["uids"],
                        total_found=data["total_found"],
                        processing_time=data["processing_time"],
                        metadata=data["metadata"]
                    )
                else:
                    raise UIDParsingError(f"HTML parsing failed: {result.get('error')}")
                    
        except Exception as e:
            logger.error(f"Failed to parse HTML content: {e}")
            raise UIDParsingError(f"HTML parsing failed: {e}")
    
    async def parse_html_stream(
        self, 
        html_stream: AsyncGenerator[HTMLContent, None]
    ) -> AsyncGenerator[UIDExtractionResult, None]:
        """Parse streaming HTML content"""
        try:
            batch_size = 10  # Process in batches for efficiency
            batch = []
            
            async for html_content in html_stream:
                batch.append(html_content)
                
                if len(batch) >= batch_size:
                    # Process batch
                    results = await self._parse_batch(batch)
                    for result in results:
                        if result:
                            yield result
                    batch = []
            
            # Process remaining items
            if batch:
                results = await self._parse_batch(batch)
                for result in results:
                    if result:
                        yield result
                        
        except Exception as e:
            logger.error(f"Stream parsing failed: {e}")
            raise UIDParsingError(f"Stream parsing failed: {e}")
    
    async def _parse_batch(self, html_contents: List[HTMLContent]) -> List[Optional[UIDExtractionResult]]:
        """Parse a batch of HTML contents"""
        try:
            if not self.session:
                raise UIDParsingError("Client not initialized")
            
            request_data = []
            for html_content in html_contents:
                request_data.append({
                    "html_content": {
                        "content": html_content.content,
                        "url": html_content.url,
                        "timestamp": html_content.timestamp,
                        "metadata": html_content.metadata
                    },
                    "options": {
                        "parallel_workers": self.config.parallel_workers,
                        "cache_enabled": self.config.cache_enabled
                    }
                })
            
            start_time = time.time()
            
            async with self.session.post(
                f"{self.service_url}/batch-parse",
                json=request_data
            ) as response:
                results = await response.json()
                
                processing_time = time.time() - start_time
                
                parsed_results = []
                for result in results:
                    if result.get("success"):
                        data = result["data"]
                        
                        # Update local stats
                        self.stats["total_requests"] += 1
                        self.stats["total_uids"] += data["total_found"]
                        self.stats["total_processing_time"] += processing_time / len(results)
                        
                        parsed_results.append(UIDExtractionResult(
                            uids=data["uids"],
                            total_found=data["total_found"],
                            processing_time=data["processing_time"],
                            metadata=data["metadata"]
                        ))
                    else:
                        logger.warning(f"Batch item parsing failed: {result.get('error')}")
                        parsed_results.append(None)
                
                return parsed_results
                
        except Exception as e:
            logger.error(f"Batch parsing failed: {e}")
            return [None] * len(html_contents)
    
    async def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get parsing performance statistics"""
        try:
            if not self.session:
                return self.stats
            
            # Get remote stats
            async with self.session.get(f"{self.service_url}/stats") as response:
                if response.status == 200:
                    remote_stats = await response.json()
                    
                    # Combine with local stats
                    combined_stats = {
                        **self.stats,
                        "remote_stats": remote_stats,
                        "service_status": self.status.value
                    }
                    
                    return combined_stats
                else:
                    logger.warning(f"Failed to get remote stats: {response.status}")
                    return self.stats
                    
        except Exception as e:
            logger.error(f"Failed to get parsing statistics: {e}")
            return self.stats
    
    async def clear_cache(self) -> bool:
        """Clear parsing cache"""
        try:
            if not self.session:
                return False
            
            async with self.session.post(f"{self.service_url}/clear-cache") as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("success", False)
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
            return False
    
    async def get_service_status(self) -> ServiceStatus:
        """Get service status"""
        try:
            if not self.session:
                return ServiceStatus.ERROR
            
            async with self.session.get(f"{self.service_url}/health") as response:
                if response.status == 200:
                    return ServiceStatus.READY
                else:
                    return ServiceStatus.ERROR
                    
        except Exception as e:
            logger.error(f"Failed to get service status: {e}")
            return ServiceStatus.ERROR
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            self.status = ServiceStatus.STOPPED
            logger.info("CollyParser client cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")


class CollyParserManager:
    """Manager for CollyParser instances with load balancing"""
    
    def __init__(self, config: CollyConfig, max_instances: int = 3):
        self.config = config
        self.max_instances = max_instances
        self.instances: List[CollyParserClient] = []
        self.current_instance = 0
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the manager and create instances"""
        try:
            # Create instances
            for i in range(self.max_instances):
                client = CollyParserClient(self.config)
                if await client.initialize():
                    self.instances.append(client)
                else:
                    logger.error(f"Failed to initialize CollyParser instance {i}")
                    return False
            
            self._initialized = True
            logger.info(f"CollyParserManager initialized with {len(self.instances)} instances")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CollyParserManager: {e}")
            return False
    
    def _get_next_instance(self) -> CollyParserClient:
        """Get next instance using round-robin"""
        if not self._initialized or not self.instances:
            raise UIDParsingError("Manager not initialized")
        
        instance = self.instances[self.current_instance]
        self.current_instance = (self.current_instance + 1) % len(self.instances)
        return instance
    
    async def parse_html_with_load_balancing(
        self, 
        html_content: HTMLContent
    ) -> UIDExtractionResult:
        """Parse HTML with load balancing across instances"""
        instance = self._get_next_instance()
        return await instance.parse_html_content(html_content)
    
    async def parse_multiple_html(
        self, 
        html_contents: List[HTMLContent]
    ) -> List[UIDExtractionResult]:
        """Parse multiple HTML contents in parallel"""
        if not self._initialized:
            raise UIDParsingError("Manager not initialized")
        
        # Distribute work across instances
        tasks = []
        for i, html_content in enumerate(html_contents):
            instance = self.instances[i % len(self.instances)]
            task = asyncio.create_task(instance.parse_html_content(html_content))
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Parsing failed for item {i}: {result}")
                # Create empty result
                final_results.append(UIDExtractionResult(
                    uids=[],
                    total_found=0,
                    processing_time=0.0,
                    metadata={"error": str(result)}
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    async def get_combined_statistics(self) -> Dict[str, Any]:
        """Get combined statistics from all instances"""
        try:
            stats_tasks = [instance.get_parsing_statistics() for instance in self.instances]
            all_stats = await asyncio.gather(*stats_tasks, return_exceptions=True)
            
            combined = {
                "total_instances": len(self.instances),
                "instance_stats": [],
                "aggregate": {
                    "total_requests": 0,
                    "total_uids": 0,
                    "total_processing_time": 0.0,
                    "average_processing_time": 0.0
                }
            }
            
            for i, stats in enumerate(all_stats):
                if not isinstance(stats, Exception):
                    combined["instance_stats"].append({
                        "instance_id": i,
                        "stats": stats
                    })
                    
                    # Aggregate stats
                    combined["aggregate"]["total_requests"] += stats.get("total_requests", 0)
                    combined["aggregate"]["total_uids"] += stats.get("total_uids", 0)
                    combined["aggregate"]["total_processing_time"] += stats.get("total_processing_time", 0.0)
            
            # Calculate average
            if combined["aggregate"]["total_requests"] > 0:
                combined["aggregate"]["average_processing_time"] = (
                    combined["aggregate"]["total_processing_time"] / 
                    combined["aggregate"]["total_requests"]
                )
            
            return combined
            
        except Exception as e:
            logger.error(f"Failed to get combined statistics: {e}")
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup all instances"""
        try:
            cleanup_tasks = [instance.cleanup() for instance in self.instances]
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self.instances.clear()
            self._initialized = False
            logger.info("CollyParserManager cleaned up")
            
        except Exception as e:
            logger.error(f"Manager cleanup error: {e}")
