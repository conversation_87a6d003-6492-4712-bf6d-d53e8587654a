#!/usr/bin/env python3
"""
Test script to understand zendriver API
"""
import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import zendriver from local installation
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "zendriver_local"))

try:
    import zendriver
    print("✅ Zendriver imported successfully")
except ImportError as e:
    print(f"❌ Failed to import zendriver: {e}")
    sys.exit(1)

async def test_zendriver_api():
    """Test zendriver API to understand correct methods"""
    print("🧪 Testing Zendriver API...")
    
    try:
        # Launch browser
        browser = await zendriver.start(
            browser_executable_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            sandbox=False,
            headless=False
        )
        print("✅ Browser launched successfully")
        
        # Test different methods to get page
        print("\n🔍 Testing page access methods:")
        
        # Method 1: Check if browser has pages attribute
        if hasattr(browser, 'pages'):
            print("✅ Browser has 'pages' attribute")
            pages = browser.pages
            print(f"   Pages count: {len(pages) if pages else 0}")
        else:
            print("❌ Browser does not have 'pages' attribute")
        
        # Method 2: Check if browser has get_page method
        if hasattr(browser, 'get_page'):
            print("✅ Browser has 'get_page' method")
            try:
                page = await browser.get_page()
                print(f"   Page obtained: {page is not None}")
            except Exception as e:
                print(f"   Error calling get_page(): {e}")
        else:
            print("❌ Browser does not have 'get_page' method")
        
        # Method 3: Check if browser has current_page attribute
        if hasattr(browser, 'current_page'):
            print("✅ Browser has 'current_page' attribute")
            page = browser.current_page
            print(f"   Current page: {page is not None}")
        else:
            print("❌ Browser does not have 'current_page' attribute")
        
        # Method 4: Check if browser has get_current_page method
        if hasattr(browser, 'get_current_page'):
            print("✅ Browser has 'get_current_page' method")
            try:
                page = await browser.get_current_page()
                print(f"   Current page obtained: {page is not None}")
            except Exception as e:
                print(f"   Error calling get_current_page(): {e}")
        else:
            print("❌ Browser does not have 'get_current_page' method")
        
        # Method 5: Check all available methods
        print("\n📋 All browser methods and attributes:")
        methods = [attr for attr in dir(browser) if not attr.startswith('_')]
        for method in sorted(methods):
            print(f"   - {method}")
        
        # Try to navigate to a page
        print("\n🌐 Testing navigation...")
        try:
            if hasattr(browser, 'get'):
                await browser.get("https://www.google.com")
                print("✅ Navigation successful using browser.get()")
            elif hasattr(browser, 'goto'):
                await browser.goto("https://www.google.com")
                print("✅ Navigation successful using browser.goto()")
            else:
                print("❌ No navigation method found")
        except Exception as e:
            print(f"❌ Navigation failed: {e}")
        
        # Close browser
        await browser.quit()
        print("✅ Browser closed successfully")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_zendriver_api())
