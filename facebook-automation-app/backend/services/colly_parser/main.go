package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/debug"
	"github.com/gorilla/mux"
	"github.com/patrickmn/go-cache"
)

// Configuration
type Config struct {
	Port                   string        `json:"port"`
	ParallelWorkers        int           `json:"parallel_workers"`
	RequestTimeout         time.Duration `json:"request_timeout"`
	MaxConcurrentRequests  int           `json:"max_concurrent_requests"`
	CacheEnabled           bool          `json:"cache_enabled"`
	CacheSizeMB            int           `json:"cache_size_mb"`
	CacheExpiration        time.Duration `json:"cache_expiration"`
	CacheCleanupInterval   time.Duration `json:"cache_cleanup_interval"`
}

// Data structures
type HTMLContent struct {
	Content   string                 `json:"content"`
	URL       string                 `json:"url"`
	Timestamp float64                `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

type UIDExtractionResult struct {
	UIDs           []string               `json:"uids"`
	TotalFound     int                    `json:"total_found"`
	ProcessingTime float64                `json:"processing_time"`
	Metadata       map[string]interface{} `json:"metadata"`
}

type ParseRequest struct {
	HTMLContent HTMLContent            `json:"html_content"`
	Options     map[string]interface{} `json:"options"`
}

type ParseResponse struct {
	Success bool                `json:"success"`
	Data    UIDExtractionResult `json:"data,omitempty"`
	Error   string              `json:"error,omitempty"`
}

// UID extraction patterns
var uidPatterns = []*regexp.Regexp{
	// Facebook profile URLs
	regexp.MustCompile(`facebook\.com/profile\.php\?id=(\d{8,20})`),
	regexp.MustCompile(`facebook\.com/(\d{8,20})`),
	
	// Facebook group user URLs
	regexp.MustCompile(`/groups/\d+/user/(\d{8,20})`),
	
	// Data attributes
	regexp.MustCompile(`data-profileid="(\d{8,20})"`),
	regexp.MustCompile(`data-userid="(\d{8,20})"`),
	regexp.MustCompile(`data-id="(\d{8,20})"`),
	
	// JavaScript variables
	regexp.MustCompile(`"id":"(\d{8,20})"`),
	regexp.MustCompile(`'id':'(\d{8,20})'`),
	
	// Comment and post IDs
	regexp.MustCompile(`comment_id=(\d{8,20})`),
	regexp.MustCompile(`post_id=(\d{8,20})`),
	
	// Generic numeric IDs in URLs
	regexp.MustCompile(`[?&]id=(\d{8,20})`),
	regexp.MustCompile(`/(\d{8,20})/`),
}

// CollyParser service
type CollyParser struct {
	config     *Config
	collector  *colly.Collector
	cache      *cache.Cache
	uidCache   map[string]bool
	uidCacheMu sync.RWMutex
	stats      struct {
		TotalRequests   int64
		TotalUIDs       int64
		CacheHits       int64
		ProcessingTime  time.Duration
		mu              sync.RWMutex
	}
}

func NewCollyParser(config *Config) *CollyParser {
	// Create collector with configuration
	c := colly.NewCollector(
		colly.Debugger(&debug.LogDebugger{}),
		colly.Async(true),
	)
	
	// Configure limits
	c.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: config.ParallelWorkers,
	})
	
	// Set timeouts
	c.SetRequestTimeout(config.RequestTimeout)
	
	// Create cache if enabled
	var cacheInstance *cache.Cache
	if config.CacheEnabled {
		cacheInstance = cache.New(config.CacheExpiration, config.CacheCleanupInterval)
	}
	
	return &CollyParser{
		config:    config,
		collector: c,
		cache:     cacheInstance,
		uidCache:  make(map[string]bool),
	}
}

// Extract UIDs from text using regex patterns
func (cp *CollyParser) extractUIDsFromText(text string) []string {
	uidSet := make(map[string]bool)
	
	for _, pattern := range uidPatterns {
		matches := pattern.FindAllStringSubmatch(text, -1)
		for _, match := range matches {
			if len(match) > 1 {
				uid := match[1]
				// Validate UID (numeric and reasonable length)
				if cp.isValidUID(uid) {
					uidSet[uid] = true
				}
			}
		}
	}
	
	// Convert set to slice
	uids := make([]string, 0, len(uidSet))
	for uid := range uidSet {
		uids = append(uids, uid)
	}
	
	return uids
}

// Extract UIDs from HTML using goquery
func (cp *CollyParser) extractUIDsFromHTML(htmlContent string) []string {
	uidSet := make(map[string]bool)
	
	// Parse HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		log.Printf("Error parsing HTML: %v", err)
		return []string{}
	}
	
	// Extract from text content
	textUIDs := cp.extractUIDsFromText(htmlContent)
	for _, uid := range textUIDs {
		uidSet[uid] = true
	}
	
	// Extract from specific attributes
	doc.Find("*").Each(func(i int, s *goquery.Selection) {
		// Check data attributes
		if profileID, exists := s.Attr("data-profileid"); exists && cp.isValidUID(profileID) {
			uidSet[profileID] = true
		}
		if userID, exists := s.Attr("data-userid"); exists && cp.isValidUID(userID) {
			uidSet[userID] = true
		}
		if id, exists := s.Attr("data-id"); exists && cp.isValidUID(id) {
			uidSet[id] = true
		}
		
		// Check href attributes
		if href, exists := s.Attr("href"); exists {
			hrefUIDs := cp.extractUIDsFromText(href)
			for _, uid := range hrefUIDs {
				uidSet[uid] = true
			}
		}
	})
	
	// Convert set to slice
	uids := make([]string, 0, len(uidSet))
	for uid := range uidSet {
		uids = append(uids, uid)
	}
	
	return uids
}

// Validate UID format
func (cp *CollyParser) isValidUID(uid string) bool {
	// Check if numeric
	if _, err := strconv.ParseInt(uid, 10, 64); err != nil {
		return false
	}
	
	// Check length (Facebook UIDs are typically 8-20 digits)
	return len(uid) >= 8 && len(uid) <= 20
}

// Deduplicate UIDs
func (cp *CollyParser) deduplicateUIDs(uids []string) []string {
	cp.uidCacheMu.Lock()
	defer cp.uidCacheMu.Unlock()
	
	uniqueUIDs := make([]string, 0)
	for _, uid := range uids {
		if !cp.uidCache[uid] {
			cp.uidCache[uid] = true
			uniqueUIDs = append(uniqueUIDs, uid)
		}
	}
	
	return uniqueUIDs
}

// Parse HTML content and extract UIDs
func (cp *CollyParser) parseHTMLContent(htmlContent HTMLContent) (*UIDExtractionResult, error) {
	startTime := time.Now()
	
	// Check cache first
	if cp.cache != nil {
		cacheKey := fmt.Sprintf("html_%x", len(htmlContent.Content))
		if cached, found := cp.cache.Get(cacheKey); found {
			cp.stats.mu.Lock()
			cp.stats.CacheHits++
			cp.stats.mu.Unlock()
			
			if result, ok := cached.(*UIDExtractionResult); ok {
				return result, nil
			}
		}
	}
	
	// Extract UIDs from HTML
	uids := cp.extractUIDsFromHTML(htmlContent.Content)
	
	// Deduplicate
	uniqueUIDs := cp.deduplicateUIDs(uids)
	
	processingTime := time.Since(startTime)
	
	result := &UIDExtractionResult{
		UIDs:           uniqueUIDs,
		TotalFound:     len(uids),
		ProcessingTime: processingTime.Seconds(),
		Metadata: map[string]interface{}{
			"source_url":        htmlContent.URL,
			"content_length":    len(htmlContent.Content),
			"extraction_method": "colly_goquery",
			"unique_uids":       len(uniqueUIDs),
			"duplicate_count":   len(uids) - len(uniqueUIDs),
		},
	}
	
	// Cache result
	if cp.cache != nil {
		cacheKey := fmt.Sprintf("html_%x", len(htmlContent.Content))
		cp.cache.Set(cacheKey, result, cache.DefaultExpiration)
	}
	
	// Update statistics
	cp.stats.mu.Lock()
	cp.stats.TotalRequests++
	cp.stats.TotalUIDs += int64(len(uniqueUIDs))
	cp.stats.ProcessingTime += processingTime
	cp.stats.mu.Unlock()
	
	return result, nil
}

// HTTP Handlers
func (cp *CollyParser) handleParseHTML(w http.ResponseWriter, r *http.Request) {
	var req ParseRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	result, err := cp.parseHTMLContent(req.HTMLContent)
	if err != nil {
		response := ParseResponse{
			Success: false,
			Error:   err.Error(),
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}
	
	response := ParseResponse{
		Success: true,
		Data:    *result,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (cp *CollyParser) handleBatchParse(w http.ResponseWriter, r *http.Request) {
	var requests []ParseRequest
	if err := json.NewDecoder(r.Body).Decode(&requests); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	results := make([]ParseResponse, len(requests))
	
	// Process in parallel
	var wg sync.WaitGroup
	for i, req := range requests {
		wg.Add(1)
		go func(index int, request ParseRequest) {
			defer wg.Done()
			
			result, err := cp.parseHTMLContent(request.HTMLContent)
			if err != nil {
				results[index] = ParseResponse{
					Success: false,
					Error:   err.Error(),
				}
			} else {
				results[index] = ParseResponse{
					Success: true,
					Data:    *result,
				}
			}
		}(i, req)
	}
	
	wg.Wait()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(results)
}

func (cp *CollyParser) handleGetStats(w http.ResponseWriter, r *http.Request) {
	cp.stats.mu.RLock()
	stats := map[string]interface{}{
		"total_requests":    cp.stats.TotalRequests,
		"total_uids":        cp.stats.TotalUIDs,
		"cache_hits":        cp.stats.CacheHits,
		"processing_time":   cp.stats.ProcessingTime.Seconds(),
		"average_time":      cp.stats.ProcessingTime.Seconds() / float64(cp.stats.TotalRequests),
		"cache_enabled":     cp.config.CacheEnabled,
		"parallel_workers":  cp.config.ParallelWorkers,
	}
	cp.stats.mu.RUnlock()
	
	// Add cache statistics
	if cp.cache != nil {
		stats["cache_items"] = cp.cache.ItemCount()
	}
	
	// Add UID cache statistics
	cp.uidCacheMu.RLock()
	stats["unique_uids_cached"] = len(cp.uidCache)
	cp.uidCacheMu.RUnlock()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

func (cp *CollyParser) handleClearCache(w http.ResponseWriter, r *http.Request) {
	if cp.cache != nil {
		cp.cache.Flush()
	}
	
	cp.uidCacheMu.Lock()
	cp.uidCache = make(map[string]bool)
	cp.uidCacheMu.Unlock()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

func main() {
	// Load configuration
	config := &Config{
		Port:                  ":8082",
		ParallelWorkers:       8,
		RequestTimeout:        30 * time.Second,
		MaxConcurrentRequests: 100,
		CacheEnabled:          true,
		CacheSizeMB:          100,
		CacheExpiration:      5 * time.Minute,
		CacheCleanupInterval: 10 * time.Minute,
	}
	
	// Create service
	parser := NewCollyParser(config)
	
	// Setup routes
	router := mux.NewRouter()
	router.HandleFunc("/parse", parser.handleParseHTML).Methods("POST")
	router.HandleFunc("/batch-parse", parser.handleBatchParse).Methods("POST")
	router.HandleFunc("/stats", parser.handleGetStats).Methods("GET")
	router.HandleFunc("/clear-cache", parser.handleClearCache).Methods("POST")
	
	// Health check
	router.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
	}).Methods("GET")
	
	// Start server
	server := &http.Server{
		Addr:    config.Port,
		Handler: router,
	}
	
	// Graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan
		
		log.Println("Shutting down CollyParser service...")
		server.Shutdown(context.Background())
	}()
	
	log.Printf("CollyParser service starting on port %s", config.Port)
	if err := server.ListenAndServe(); err != http.ErrServerClosed {
		log.Fatalf("Server failed to start: %v", err)
	}
}
