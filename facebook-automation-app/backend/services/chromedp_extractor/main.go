package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/chromedp/chromedp"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

// Configuration
type Config struct {
	Port                    string        `json:"port"`
	ConnectionPoolSize      int           `json:"connection_pool_size"`
	ConnectionTimeout       time.Duration `json:"connection_timeout"`
	MaxRetries              int           `json:"max_retries"`
	DOMMonitoringInterval   time.Duration `json:"dom_monitoring_interval"`
	HTMLSnapshotTimeout     time.Duration `json:"html_snapshot_timeout"`
}

// Data structures
type HTMLContent struct {
	Content   string            `json:"content"`
	URL       string            `json:"url"`
	Timestamp float64           `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

type BrowserInfo struct {
	ProfileID    string `json:"profile_id"`
	DebuggerURL  string `json:"debugger_url"`
	WebSocketURL string `json:"websocket_url"`
	TabID        string `json:"tab_id"`
}

type ExtractionRequest struct {
	BrowserInfo BrowserInfo `json:"browser_info"`
	Options     map[string]interface{} `json:"options"`
}

type ExtractionResponse struct {
	Success bool        `json:"success"`
	Data    HTMLContent `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// Connection pool for chromedp contexts
type ConnectionPool struct {
	connections chan *chromedp.Context
	mu          sync.RWMutex
	config      *Config
}

func NewConnectionPool(config *Config) *ConnectionPool {
	pool := &ConnectionPool{
		connections: make(chan *chromedp.Context, config.ConnectionPoolSize),
		config:      config,
	}
	
	// Initialize pool with connections
	for i := 0; i < config.ConnectionPoolSize; i++ {
		ctx, cancel := chromedp.NewContext(context.Background())
		// Store cancel function for cleanup
		go func() {
			<-ctx.Done()
			cancel()
		}()
		pool.connections <- ctx
	}
	
	return pool
}

func (p *ConnectionPool) Get() *chromedp.Context {
	return <-p.connections
}

func (p *ConnectionPool) Put(ctx *chromedp.Context) {
	select {
	case p.connections <- ctx:
	default:
		// Pool is full, discard connection
	}
}

func (p *ConnectionPool) Close() {
	close(p.connections)
	for ctx := range p.connections {
		ctx.Done()
	}
}

// ChromedpExtractor service
type ChromedpExtractor struct {
	config         *Config
	connectionPool *ConnectionPool
	activeContexts map[string]*chromedp.Context
	mu             sync.RWMutex
	upgrader       websocket.Upgrader
}

func NewChromedpExtractor(config *Config) *ChromedpExtractor {
	return &ChromedpExtractor{
		config:         config,
		connectionPool: NewConnectionPool(config),
		activeContexts: make(map[string]*chromedp.Context),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for development
			},
		},
	}
}

// Connect to existing browser instance
func (ce *ChromedpExtractor) connectToBrowser(browserInfo BrowserInfo) (*chromedp.Context, error) {
	// Create allocator that connects to existing browser
	allocCtx, cancel := chromedp.NewRemoteAllocator(context.Background(), browserInfo.WebSocketURL)
	defer cancel()
	
	// Create context with the allocator
	ctx, cancel := chromedp.NewContext(allocCtx)
	
	// Store context for cleanup
	ce.mu.Lock()
	ce.activeContexts[browserInfo.ProfileID] = ctx
	ce.mu.Unlock()
	
	// Test connection
	var title string
	err := chromedp.Run(ctx, chromedp.Title(&title))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to browser: %v", err)
	}
	
	return ctx, nil
}

// Extract HTML snapshot
func (ce *ChromedpExtractor) extractHTMLSnapshot(ctx *chromedp.Context, url string) (*HTMLContent, error) {
	var htmlContent string
	var currentURL string
	
	// Create timeout context
	timeoutCtx, cancel := context.WithTimeout(ctx, ce.config.HTMLSnapshotTimeout)
	defer cancel()
	
	// Extract HTML content
	err := chromedp.Run(timeoutCtx,
		chromedp.Location(&currentURL),
		chromedp.OuterHTML("html", &htmlContent),
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to extract HTML: %v", err)
	}
	
	return &HTMLContent{
		Content:   htmlContent,
		URL:       currentURL,
		Timestamp: float64(time.Now().Unix()),
		Metadata: map[string]interface{}{
			"extraction_method": "chromedp_snapshot",
			"content_length":    len(htmlContent),
		},
	}, nil
}

// Monitor DOM changes
func (ce *ChromedpExtractor) monitorDOMChanges(ctx *chromedp.Context, url string, callback func(*HTMLContent)) error {
	ticker := time.NewTicker(ce.config.DOMMonitoringInterval)
	defer ticker.Stop()
	
	var lastContentHash string
	
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			content, err := ce.extractHTMLSnapshot(ctx, url)
			if err != nil {
				log.Printf("Error extracting HTML during monitoring: %v", err)
				continue
			}
			
			// Simple hash to detect changes
			currentHash := fmt.Sprintf("%x", len(content.Content))
			if currentHash != lastContentHash {
				lastContentHash = currentHash
				callback(content)
			}
		}
	}
}

// HTTP Handlers
func (ce *ChromedpExtractor) handleConnect(w http.ResponseWriter, r *http.Request) {
	var req ExtractionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	ctx, err := ce.connectToBrowser(req.BrowserInfo)
	if err != nil {
		response := ExtractionResponse{
			Success: false,
			Error:   err.Error(),
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}
	
	response := ExtractionResponse{
		Success: true,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (ce *ChromedpExtractor) handleExtractHTML(w http.ResponseWriter, r *http.Request) {
	var req ExtractionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	
	// Get context for profile
	ce.mu.RLock()
	ctx, exists := ce.activeContexts[req.BrowserInfo.ProfileID]
	ce.mu.RUnlock()
	
	if !exists {
		// Try to connect
		var err error
		ctx, err = ce.connectToBrowser(req.BrowserInfo)
		if err != nil {
			response := ExtractionResponse{
				Success: false,
				Error:   fmt.Sprintf("No active context for profile %s: %v", req.BrowserInfo.ProfileID, err),
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
			return
		}
	}
	
	// Extract HTML
	content, err := ce.extractHTMLSnapshot(ctx, req.BrowserInfo.WebSocketURL)
	if err != nil {
		response := ExtractionResponse{
			Success: false,
			Error:   err.Error(),
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}
	
	response := ExtractionResponse{
		Success: true,
		Data:    *content,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (ce *ChromedpExtractor) handleMonitorDOM(w http.ResponseWriter, r *http.Request) {
	// Upgrade to WebSocket
	conn, err := ce.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()
	
	// Read initial request
	var req ExtractionRequest
	if err := conn.ReadJSON(&req); err != nil {
		log.Printf("Failed to read WebSocket request: %v", err)
		return
	}
	
	// Get or create context
	ce.mu.RLock()
	ctx, exists := ce.activeContexts[req.BrowserInfo.ProfileID]
	ce.mu.RUnlock()
	
	if !exists {
		ctx, err = ce.connectToBrowser(req.BrowserInfo)
		if err != nil {
			conn.WriteJSON(map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}
	
	// Start monitoring
	go ce.monitorDOMChanges(ctx, req.BrowserInfo.WebSocketURL, func(content *HTMLContent) {
		conn.WriteJSON(map[string]interface{}{
			"success": true,
			"data":    content,
		})
	})
	
	// Keep connection alive
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}
}

func (ce *ChromedpExtractor) handleDisconnect(w http.ResponseWriter, r *http.Request) {
	profileID := r.URL.Query().Get("profile_id")
	if profileID == "" {
		http.Error(w, "profile_id parameter required", http.StatusBadRequest)
		return
	}
	
	ce.mu.Lock()
	if ctx, exists := ce.activeContexts[profileID]; exists {
		ctx.Done()
		delete(ce.activeContexts, profileID)
	}
	ce.mu.Unlock()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

func main() {
	// Load configuration
	config := &Config{
		Port:                  ":8081",
		ConnectionPoolSize:    10,
		ConnectionTimeout:     30 * time.Second,
		MaxRetries:           3,
		DOMMonitoringInterval: 1 * time.Second,
		HTMLSnapshotTimeout:   60 * time.Second,
	}
	
	// Create service
	extractor := NewChromedpExtractor(config)
	
	// Setup routes
	router := mux.NewRouter()
	router.HandleFunc("/connect", extractor.handleConnect).Methods("POST")
	router.HandleFunc("/extract", extractor.handleExtractHTML).Methods("POST")
	router.HandleFunc("/monitor", extractor.handleMonitorDOM)
	router.HandleFunc("/disconnect", extractor.handleDisconnect).Methods("POST")
	
	// Health check
	router.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
	}).Methods("GET")
	
	// Start server
	server := &http.Server{
		Addr:    config.Port,
		Handler: router,
	}
	
	// Graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan
		
		log.Println("Shutting down ChromedpExtractor service...")
		extractor.connectionPool.Close()
		server.Shutdown(context.Background())
	}()
	
	log.Printf("ChromedpExtractor service starting on port %s", config.Port)
	if err := server.ListenAndServe(); err != http.ErrServerClosed {
		log.Fatalf("Server failed to start: %v", err)
	}
}
