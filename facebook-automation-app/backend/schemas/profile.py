"""
Profile schemas for API requests and responses
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Union
from datetime import datetime

class ProfileBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    user_agent: Optional[str] = None
    screen_resolution: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None

class ProfileCreate(ProfileBase):
    # Proxy configuration
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    proxy_type: Optional[str] = None
    
    # Facebook account info
    facebook_email: Optional[str] = None
    facebook_password: Optional[str] = None
    facebook_username: Optional[str] = None

class ProfileUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    user_agent: Optional[str] = None
    screen_resolution: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    
    # Proxy configuration
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    proxy_type: Optional[str] = None
    
    # Facebook account info
    facebook_email: Optional[str] = None
    facebook_password: Optional[str] = None
    facebook_username: Optional[str] = None
    
    status: Optional[str] = None

class ProfileResponse(ProfileBase):
    id: str
    status: Optional[str] = None
    profile_path: Optional[str] = None
    last_used_at: Optional[Union[datetime, str]] = None
    created_at: Optional[Union[datetime, str]] = None
    updated_at: Optional[Union[datetime, str]] = None
    login_count: int = 0
    message_sent_count: int = 0

    # Facebook info (without sensitive data)
    facebook_email: Optional[str] = None
    facebook_user_id: Optional[str] = None
    facebook_username: Optional[str] = None
    facebook_logged_in: bool = False

    # Proxy info (without sensitive data)
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_type: Optional[str] = None
    proxy_config: Optional[dict] = None

    @field_validator('last_used_at', 'created_at', 'updated_at', mode='before')
    @classmethod
    def convert_datetime_to_str(cls, v):
        if isinstance(v, datetime):
            return v.isoformat()
        return v



    class Config:
        from_attributes = True

class ProfileList(BaseModel):
    profiles: List[ProfileResponse]
    total: int
    page: int
    per_page: int
    
class ProfileStats(BaseModel):
    total_profiles: int
    active_profiles: int
    inactive_profiles: int
    banned_profiles: int
    error_profiles: int

class BrowserSessionStatus(BaseModel):
    status: str
    session_id: Optional[str] = None
    message: Optional[str] = None
    elapsed_time: Optional[float] = None
    timeout_remaining: Optional[float] = None

class FacebookLoginResponse(BaseModel):
    status: str
    message: str
    profile_id: str
    profile_name: str
    session_id: Optional[str] = None
    browser_status: Optional[str] = None
    login_url: Optional[str] = None
    instructions: Optional[List[str]] = None
    facebook_result: Optional[dict] = None
