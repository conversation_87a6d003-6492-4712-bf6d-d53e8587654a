on: [push, pull_request]
name: Test
jobs:
  test:
    strategy:
      matrix:
        go-version: [oldstable, stable]
    runs-on: ubuntu-latest
    steps:
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go-version }}
      - name: Install Packages
        run: |
          sudo apt-get -qq update
          sudo apt-get install -y build-essential
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Test Chrome
        run: go test -v ./...
      - name: Test headless-shell
        run: ./contrib/docker-test.sh
