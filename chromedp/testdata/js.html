<!doctype html>
<html>
<head>
  <style>
    body {
        background-color: antiquewhite;
    }
    input[type="number"] {
        background-color: red;
        color: white;
    }
    input[type="number"]:focus {
      background-color: yellow;
      color: red;
    }
  </style>
</head>
<body style="background-color: white">
  <input id="input1" type="number" value="0"/>
  <input id="button1" type="button" value="Click me" ondblclick="increment()">
  <hr>
  <input id="input2" type="text" value="foo"/>
  <input id="button2" type="button" value="Click me" onclick="show()">
  <hr>
  <input id="button3" type="button" value="Click me" onclick="enable()">
  <select id="select1" disabled>
    <option value="foo">foo</option>
    <option value="bar" selected>bar</option>
  </select>
  <hr>
  <input id="input3" type="text" value=""/>
  <input id="button4" type="button" value="Click me" onclick="remove()">
  <script>
    document.getElementById('input1').onfocus = function() {
      this.value = '9999'
    };
    document.getElementById('input1').onblur = function() {
      this.value = '0'
    };
    function increment() {
      document.getElementById('input1').value++;
      return false;
    }
    function show() {
      document.getElementById('input2').style.display = 'none';
      return false;
    }
    function enable() {
      document.getElementById('select1').disabled = false;
      return false;
    }
    function remove() {
      document.body.removeChild(document.getElementById('input3'));
      return false;
    }
  </script>
</body>
</html>
