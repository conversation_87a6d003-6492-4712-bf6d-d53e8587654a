<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>screenshot</title>
    <style>
        #padding-border {
            border: 2px solid blue;
            background: green;
            width: 50px;
            height: 50px;
        }

        #larger-than-viewport {
            border: 1px solid blue;
            width: 600px;
            height: 600px;
            margin-left: 50px;
        }

        #above {
            border: 2px solid blue;
            background: red;
            height: 1500px;
        }

        #outside-viewport {
            border: 2px solid blue;
            background: green;
            width: 50px;
            height: 50px;
        }

        #rotated {
            position: absolute;
            top: 100px;
            right: 100px;
            width: 100px;
            height: 100px;
            background: green;
            transform: rotateZ(200deg);
        }

        #fractional-dimensions {
            width: 48.51px;
            height: 19.8px;
            border: 1px solid black;
        }

        #fractional-offset {
            position: absolute;
            top: 10.3px;
            left: 200.4px;
            width: 50.3px;
            height: 20.2px;
            border: 1px solid black;
        }

        #svg-circle {
            width: 20.82px;
            height: 20.82px;
        }

        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
<div id="padding-border"></div>
<div id="larger-than-viewport"></div>
<div id="above"></div>
<div id="outside-viewport"></div>
<div id="rotated"></div>
<div id="fractional-dimensions"></div>
<div id="fractional-offset" ></div>
<div id="svg-circle">
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <circle cx="50" cy="50" r="50" />
  </svg>
</div>
</body>
</html>
